/* Component styles */

/* Button styles */
button, .btn, .control-btn, .file-upload-area {
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Primary button styles */
button, .btn {
    background-color: var(--google-primary);
    color: var(--google-on-primary);
    border: none;
    border-radius: var(--radius-sm);
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: var(--font-size-md);
    font-weight: var(--font-weight-medium);
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    min-height: var(--button-height);
    text-decoration: none;
    user-select: none;
}

button:hover, .btn:hover {
    background-color: var(--google-primary-variant);
    box-shadow: var(--elevation-1);
}

button:active, .btn:active {
    transform: translateY(1px);
}

button:disabled, .btn:disabled {
    background-color: var(--google-surface-variant);
    color: var(--google-on-surface-disabled);
    cursor: not-allowed;
}

/* Secondary button styles */
.btn-secondary, .text-button {
    background-color: transparent;
    color: var(--google-primary);
    border: 1px solid var(--google-border);
}

.btn-secondary:hover, .text-button:hover {
    background-color: var(--google-hover-overlay);
    border-color: var(--google-primary);
}

/* Small button variant */
.btn-sm {
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: var(--font-size-sm);
    min-height: 28px;
}

/* Input field styles */
input[type="text"], input[type="file"], input[type="search"], textarea, select {
    background-color: var(--google-surface-variant);
    color: var(--google-on-surface);
    border: 1px solid var(--google-border);
    border-radius: var(--radius-sm);
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: var(--font-size-md);
    font-family: var(--font-family-default);
    transition: border-color 0.2s, box-shadow 0.2s;
    width: 100%;
}

input[type="text"]:focus, input[type="file"]:focus, input[type="search"]:focus, textarea:focus, select:focus {
    outline: none;
    border-color: var(--google-primary);
    box-shadow: 0 0 0 2px rgba(138, 180, 248, 0.2);
}

input[type="text"]:disabled, textarea:disabled, select:disabled {
    background-color: var(--google-surface);
    color: var(--google-on-surface-disabled);
    cursor: not-allowed;
}

/* File input special styling */
input[type="file"] {
    padding: var(--spacing-md);
    background-color: var(--google-surface);
    border: 2px dashed var(--google-border);
    border-radius: var(--radius-md);
    cursor: pointer;
}

input[type="file"]:hover {
    border-color: var(--google-primary);
    background-color: var(--google-hover-overlay);
}

/* Focus states for accessibility */
button:focus, .btn:focus, .control-btn:focus {
    outline: 2px solid var(--google-primary);
    outline-offset: 2px;
}

/* Section title styles */
.section-title.imports-title { 
    color: #f44336; /* Red for imports - matches import indicator */
    background-color: rgba(244, 67, 54, 0.15); 
}

.section-title.exports-title { 
    color: #4caf50; /* Green for exports - matches export indicator */
    background-color: rgba(76, 175, 80, 0.15); 
}

.section-title.functions-title { 
    color: #7986cb; /* Blue/indigo for functions */
    background-color: rgba(121, 134, 203, 0.15); 
}

.section-title.variables-title { 
    color: #2196f3; /* Blue for variables */
    background-color: rgba(33, 150, 243, 0.15); 
}

.section-title.events-title { 
    color: #ffc107; /* Yellow - matches event indicator */
    background-color: rgba(255, 193, 7, 0.15); 
}

.no-data-warning {
    background-color: rgba(255, 87, 34, 0.1);
    border-left: 3px solid #ff5722;
    color: #ff5722;
    padding: 8px 12px;
    margin: 8px 0;
    font-size: 12px;
}

/* App container and sidebar */
.app-container {
    display: flex;
    height: 100vh;
    overflow: hidden;
}

.sidebar {
    width: var(--sidebar-width);
    background-color: var(--google-surface);
    border-right: 1px solid var(--google-divider);
    display: flex;
    flex-direction: column;
    flex-shrink: 0;
}

.sidebar-header {
    padding: var(--spacing-md) var(--spacing-lg);
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-medium);
    color: var(--google-on-surface);
    border-bottom: 1px solid var(--google-divider);
}

.sidebar-menu {
    flex: 1;
    overflow-y: auto;
}

.sidebar-item {
    display: flex;
    align-items: center;
    padding: var(--spacing-md) var(--spacing-lg);
    color: var(--google-on-surface-medium);
    cursor: pointer;
    transition: background-color 0.2s;
}

.sidebar-item:hover {
    background-color: var(--google-hover-overlay);
}

.sidebar-item.active {
    background-color: var(--google-active-overlay);
    color: var(--google-primary);
}

.sidebar-item-icon {
    margin-right: var(--spacing-md);
}

/* Main content area */
.main-content {
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    background-color: var(--google-background);
}

.content-header {
    padding: var(--spacing-md) var(--spacing-lg);
    border-bottom: 1px solid var(--google-divider);
    background-color: var(--google-surface);
}

.content-area {
    flex: 1;
    overflow: auto;
    padding: var(--spacing-lg);
}

/* Cards */
.card {
    background-color: var(--google-card-bg);
    border-radius: var(--radius-md);
    box-shadow: var(--elevation-1);
    margin-bottom: var(--spacing-lg);
    overflow: hidden;
}

.card-header {
    padding: var(--spacing-md) var(--spacing-lg);
    background-color: var(--google-panel-header);
    border-bottom: 1px solid var(--google-divider);
    position: sticky;
    top: 0;
    z-index: 10;
}

.card-header h3 {
    margin: 0;
    font-size: var(--font-size-lg);
    font-weight: 500;
    color: var(--google-on-surface);
}

.card-body {
    padding: var(--spacing-lg);
}
