/* Visualizer specific styles */
#visualization {
    position: relative;
    overflow: hidden;
    isolation: isolate; /* Create a new stacking context */
}

#visualization canvas {
    pointer-events: auto;
    position: absolute;
    top: 0;
    left: 0;
}

.graph-minimap {
    position: absolute;
    bottom: 20px;
    right: 20px;
    background-color: rgba(30, 32, 35, 0.8);
    border: 1px solid var(--google-border);
    border-radius: var(--radius-md);
    overflow: hidden;
    z-index: 100;
}

.graph-minimap .minimap-viewport {
    position: absolute;
    border: 1px solid var(--google-primary);
    pointer-events: none;
}

/* Visualization controls */
.visualization-controls {
    position: absolute;
    top: 20px;
    right: 20px;
    display: flex;
    gap: 8px;
    z-index: 10;
}

.control-btn {
    background-color: var(--google-surface-variant);
    color: var(--google-on-surface);
    border: 1px solid var(--google-border);
    border-radius: var(--radius-md);
    padding: 8px 12px;
    font-size: var(--font-size-sm);
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 6px;
}

.control-btn:hover {
    background-color: var(--google-hover-overlay);
    border-color: var(--google-primary);
}

.control-btn:active {
    background-color: var(--google-active-overlay);
}

.control-btn svg {
    width: 16px;
    height: 16px;
}

/* Node tooltip */
.node-tooltip {
    position: absolute;
    background-color: var(--google-surface-variant);
    border: 1px solid var(--google-border);
    border-radius: var(--radius-md);
    padding: var(--spacing-md);
    box-shadow: var(--elevation-1);
    pointer-events: none;
    opacity: 0;
    transition: opacity 0.2s ease;
    max-width: 300px;
}

.node-tooltip.visible {
    opacity: 1;
}

.tooltip-title {
    font-weight: var(--font-weight-medium);
    color: var(--google-on-surface);
    margin-bottom: var(--spacing-xs);
}

.tooltip-content {
    color: var(--google-on-surface-medium);
    font-size: var(--font-size-sm);
}

/* Loading spinner */
.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(15, 20, 25, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 3px solid var(--google-border);
    border-top: 3px solid var(--google-primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Status indicators */
.status-indicator {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: var(--spacing-xs);
}

.status-indicator.success {
    background: var(--google-success);
}

.status-indicator.warning {
    background: var(--google-warning);
}

.status-indicator.error {
    background: var(--google-error);
}
