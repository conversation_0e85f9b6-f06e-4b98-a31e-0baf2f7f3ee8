/**
 * Dependency Scanner Core Module
 * Manages the scanning of files and extraction of dependencies
 */

import PathUtils from '../utils/path-utils.js';

class DependencyScanner {
    constructor(options = {}) {
        // Configuration
        this.config = {
            workerPath: options.workerPath || './js/core/scanner.worker.js',
            excludeRules: options.excludeRules || [],
            maxWorkers: options.maxWorkers || 1,
            workerTimeout: options.workerTimeout || 30000 // 30 seconds
        };

        // State
        this.workers = [];
        this.workerLastActive = {};
        this.projectRootPath = '';
        this.scanResults = null;
        this.isScanning = false;
        this.scanProgress = 0;
        this.totalFiles = 0;
        this.processedFiles = 0;
        
        // Bind methods
        this.startScan = this.startScan.bind(this);
        this.handleWorkerMessage = this.handleWorkerMessage.bind(this);
        this.terminateWorkers = this.terminateWorkers.bind(this);
    }

    /**
     * Initialize a new worker
     * @returns {Worker} - Initialized worker
     */
    initWorker() {
        const worker = new Worker(this.config.workerPath);
        const workerId = this.workers.length;
        
        worker.id = workerId;
        worker.onmessage = this.handleWorkerMessage;
        worker.onerror = (error) => {
            console.error('Worker error:', error);
            this.dispatchEvent('error', { message: `Worker error: ${error.message}` });
        };
        
        this.workerLastActive[workerId] = Date.now();
        this.workers.push(worker);
        
        return worker;
    }

    /**
     * Start scanning files
     * @param {Array} files - Array of files to scan
     * @param {string} rootPath - Project root path
     * @returns {Promise} - Promise that resolves when scanning is complete
     */
    startScan(files, rootPath) {
        if (this.isScanning) {
            return Promise.reject(new Error('Scan already in progress'));
        }
        
        this.isScanning = true;
        this.scanProgress = 0;
        this.projectRootPath = rootPath || '';
        this.totalFiles = files.length;
        this.processedFiles = 0;
        
        // Reset previous results
        this.scanResults = null;
        
        // Terminate any existing workers
        this.terminateWorkers();
        
        // Initialize a new worker
        const worker = this.initWorker();
        
        // Initialize the worker with configuration
        worker.postMessage({
            type: 'init',
            projectRootPath: this.projectRootPath,
            totalFilesToExpect: this.totalFiles,
            excludeRules: this.config.excludeRules
        });
        
        // Send files to the worker in chunks
        const chunkSize = 50;
        for (let i = 0; i < this.totalFiles; i += chunkSize) {
            const chunk = files.slice(i, i + chunkSize);
            worker.postMessage({
                type: 'fileChunk',
                files: chunk
            });
        }
        
        // Start processing
        worker.postMessage({ type: 'processAll' });
        
        // Return a promise that resolves when scanning is complete
        return new Promise((resolve, reject) => {
            this.scanCompleteResolve = resolve;
            this.scanCompleteReject = reject;
        });
    }

    /**
     * Handle messages from workers
     * @param {MessageEvent} event - Worker message event
     */
    handleWorkerMessage(event) {
        const data = event.data;
        const workerId = event.target.id;
        
        // Update last active timestamp
        this.workerLastActive[workerId] = Date.now();
        
        switch (data.type) {
            case 'processing_progress':
                this.processedFiles = data.processed;
                this.scanProgress = Math.min(100, Math.round((data.processed / this.totalFiles) * 100));
                this.dispatchEvent('progress', { 
                    processed: data.processed,
                    total: this.totalFiles,
                    percentage: this.scanProgress,
                    phase: data.phase
                });
                break;
                
            case 'workerResults':
                this.scanResults = data.data;
                this.isScanning = false;
                this.dispatchEvent('complete', { data: this.scanResults });
                
                if (this.scanCompleteResolve) {
                    this.scanCompleteResolve(this.scanResults);
                }
                break;
                
            case 'error':
                console.error('Worker reported error:', data.message);
                this.dispatchEvent('error', { message: data.message });
                
                if (this.scanCompleteReject) {
                    this.scanCompleteReject(new Error(data.message));
                }
                break;
                
            case 'debug':
                console.debug('Worker debug:', data.message);
                break;
                
            default:
                console.warn('Unknown worker message type:', data.type);
        }
    }

    /**
     * Terminate all workers with proper cleanup
     */
    terminateWorkers() {
        this.workers.forEach((worker, index) => {
            try {
                // Send cleanup message before terminating
                worker.postMessage({ type: 'cleanup' });
                
                // Give worker a moment to cleanup, then force terminate
                setTimeout(() => {
                    try {
                        worker.terminate();
                    } catch (e) {
                        console.warn(`Error terminating worker ${index}:`, e);
                    }
                }, 100);
            } catch (e) {
                console.error(`Error cleaning up worker ${index}:`, e);
                // Force terminate if cleanup message fails
                try {
                    worker.terminate();
                } catch (termError) {
                    console.error(`Error force terminating worker ${index}:`, termError);
                }
            }
        });
        
        this.workers = [];
        this.workerLastActive = {};
        
        // Force garbage collection if available
        if (window.gc) {
            setTimeout(() => window.gc(), 200);
        }
    }

    /**
     * Dispatch a custom event
     * @param {string} eventName - Name of the event
     * @param {Object} detail - Event detail data
     */
    dispatchEvent(eventName, detail = {}) {
        const event = new CustomEvent(`scanner:${eventName}`, { 
            detail,
            bubbles: true
        });
        
        document.dispatchEvent(event);
    }

    /**
     * Get the current scan results
     * @returns {Object|null} - Scan results or null if no scan has been performed
     */
    getResults() {
        return this.scanResults;
    }

    /**
     * Check if a scan is currently in progress
     * @returns {boolean} - True if scanning
     */
    isScanningInProgress() {
        return this.isScanning;
    }

    /**
     * Get the current scan progress
     * @returns {number} - Progress percentage (0-100)
     */
    getScanProgress() {
        return this.scanProgress;
    }
}

// Export the scanner class
export default DependencyScanner;
