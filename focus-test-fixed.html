<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Focus Mode Test</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
        }
        .container {
            width: 100%;
            max-width: 1000px;
            margin: 0 auto;
        }
        .instructions {
            background-color: #f3f4f6;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .svg-container {
            width: 100%;
            height: 600px;
            border: 1px solid #ccc;
            border-radius: 8px;
            overflow: hidden;
        }
        svg {
            width: 100%;
            height: 100%;
            background-color: #fafafa;
        }
        h1 {
            color: #334155;
        }
        p {
            line-height: 1.5;
        }
        .key-feature {
            font-weight: bold;
            color: #1e40af;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Focus Mode Test</h1>
        
        <div class="instructions">
            <p><span class="key-feature">Instructions:</span> Click on any node to activate focus mode. Connected dependencies will remain fully visible while unconnected ones will be dimmed (not hidden). Click the same node again or click the background to exit focus mode.</p>
            <p>This test page contains a minimal test dataset to verify the focus mode functionality.</p>
        </div>
        
        <div class="svg-container">
            <svg id="mindmap" xmlns="http://www.w3.org/2000/svg"></svg>
        </div>
    </div>

    <!-- Include the updated mindmap script with cache-busting -->
    <script src="./js/visualizers/mindmap-vanilla.js?v=1.5"></script>
    
    <script>
        // Initialize with test data
        document.addEventListener('DOMContentLoaded', function() {
            const svg = document.getElementById('mindmap');
            
            // Create a simple test dataset
            const testData = {
                nodes: [
                    {
                        id: 'node1',
                        text: 'Main Module',
                        filename: 'main.js',
                        x: 100,
                        y: 100,
                        width: 120,
                        height: 40,
                        hasImport: true,
                        hasExport: true,
                        hasEvent: true,
                        hasWorker: false
                    },
                    {
                        id: 'node2',
                        text: 'Helper Module',
                        filename: 'helper.js',
                        x: 300,
                        y: 100,
                        width: 120,
                        height: 40,
                        hasImport: true,
                        hasExport: true,
                        hasEvent: false,
                        hasWorker: false
                    },
                    {
                        id: 'node3',
                        text: 'Worker Module',
                        filename: 'worker.js',
                        x: 500,
                        y: 100,
                        width: 120,
                        height: 40,
                        hasImport: false,
                        hasExport: true,
                        hasEvent: false,
                        hasWorker: true
                    },
                    {
                        id: 'node4',
                        text: 'Event Handler',
                        filename: 'events.js',
                        x: 300,
                        y: 250,
                        width: 120,
                        height: 40,
                        hasImport: true,
                        hasExport: false,
                        hasEvent: true,
                        hasWorker: false
                    },
                    {
                        id: 'node5',
                        text: 'Utility Module',
                        filename: 'utils.js',
                        x: 500,
                        y: 250,
                        width: 120,
                        height: 40,
                        hasImport: true,
                        hasExport: true,
                        hasEvent: false,
                        hasWorker: false
                    }
                ],
                links: [
                    { source: 'node1', target: 'node2', type: 'normal' },
                    { source: 'node1', target: 'node3', type: 'worker' },
                    { source: 'node1', target: 'node4', type: 'event' },
                    { source: 'node2', target: 'node5', type: 'normal' },
                    { source: 'node4', target: 'node5', type: 'normal' }
                ]
            };
            
            // Create mindmap instance
            const mindmap = new MindmapProject(svg);
            
            // Process links into proper connection formats
            const connections = [];
            const workerConnections = [];
            const eventConnections = [];
            
            testData.links.forEach(link => {
                if (link.type === 'worker') {
                    workerConnections.push({ from: link.source, to: link.target, type: 'worker' });
                } else if (link.type === 'event') {
                    eventConnections.push({ from: link.source, to: link.target, type: 'event' });
                } else {
                    connections.push({ from: link.source, to: link.target });
                }
            });
            
            // Apply the test data
            mindmap.importData({
                nodes: testData.nodes,
                connections: connections,
                workerConnections: workerConnections,
                eventConnections: eventConnections
            });
            
            console.log('Test initialized with:', {
                nodes: testData.nodes.length,
                connections: connections.length,
                workerConnections: workerConnections.length,
                eventConnections: eventConnections.length
            });
            console.log('Click on nodes to test focus mode. All connections should remain visible, but those not connected to the selected node should be dimmed.');
        });
    </script>
</body>
</html>
