/**
 * Streaming File Reader
 * Provides memory-safe file reading with chunking and backpressure control
 */

class StreamingFileReader {
    constructor(options = {}) {
        this.maxConcurrentReads = options.maxConcurrentReads || 10;
        this.maxFileSize = options.maxFileSize || 10 * 1024 * 1024; // 10MB
        this.chunkSize = options.chunkSize || 100; // Files per chunk
        this.memoryThreshold = options.memoryThreshold || 0.7; // 70% of available memory
        this.readTimeout = options.readTimeout || 30000; // 30 seconds
        
        // State tracking
        this.activeReads = 0;
        this.totalFiles = 0;
        this.processedFiles = 0;
        this.skippedFiles = 0;
        this.errors = [];
        
        // Memory monitoring
        this.memoryMonitor = this.createMemoryMonitor();
        
        // Bind methods
        this.readFilesInChunks = this.readFilesInChunks.bind(this);
        this.readFileWithTimeout = this.readFileWithTimeout.bind(this);
        this.checkMemoryUsage = this.checkMemoryUsage.bind(this);
    }

    /**
     * Create memory monitor for tracking usage
     */
    createMemoryMonitor() {
        return {
            getUsage: () => {
                if (performance.memory) {
                    const used = performance.memory.usedJSHeapSize;
                    const limit = performance.memory.jsHeapSizeLimit;
                    return { used, limit, percentage: used / limit };
                }
                return { used: 0, limit: 0, percentage: 0 };
            },
            
            isOverThreshold: () => {
                const usage = this.memoryMonitor.getUsage();
                return usage.percentage > this.memoryThreshold;
            }
        };
    }

    /**
     * Read files in memory-safe chunks with progress reporting
     * @param {FileList|Array} files - Files to read (should already be filtered by exclude rules)
     * @param {Function} progressCallback - Progress callback function
     * @param {Function} chunkCallback - Callback for each processed chunk
     * @returns {Promise<Array>} - Promise resolving to processed file contents
     */
    async readFilesInChunks(files, progressCallback = null, chunkCallback = null) {
        // Files should already be filtered by exclude rules before reaching this point
        console.log(`Streaming File Reader: Starting to read ${files.length} pre-filtered files`);

        this.totalFiles = files.length;
        this.processedFiles = 0;
        this.skippedFiles = 0;
        this.errors = [];

        const fileArray = Array.from(files);
        const results = [];

        // Validate files before processing (additional validation beyond exclude rules)
        const validFiles = this.validateFiles(fileArray);
        
        if (progressCallback) {
            progressCallback({
                phase: 'validation',
                processed: 0,
                total: this.totalFiles,
                skipped: this.skippedFiles,
                errors: this.errors.length
            });
        }
        
        // Process files in chunks
        for (let i = 0; i < validFiles.length; i += this.chunkSize) {
            // Check memory before processing each chunk
            if (this.memoryMonitor.isOverThreshold()) {
                console.warn('Memory threshold exceeded, triggering cleanup');
                await this.triggerGarbageCollection();
                
                // If still over threshold, reduce chunk size
                if (this.memoryMonitor.isOverThreshold()) {
                    this.chunkSize = Math.max(10, Math.floor(this.chunkSize / 2));
                    console.warn(`Reduced chunk size to ${this.chunkSize} due to memory pressure`);
                }
            }
            
            const chunk = validFiles.slice(i, Math.min(i + this.chunkSize, validFiles.length));
            
            try {
                const chunkResults = await this.processChunk(chunk, progressCallback);
                results.push(...chunkResults);
                
                // Call chunk callback if provided
                if (chunkCallback) {
                    await chunkCallback(chunkResults, {
                        chunkIndex: Math.floor(i / this.chunkSize),
                        totalChunks: Math.ceil(validFiles.length / this.chunkSize),
                        processed: this.processedFiles,
                        total: this.totalFiles
                    });
                }

                // Immediate memory cleanup after each chunk
                this.cleanupChunkMemory(chunk);

                // Yield control to prevent blocking
                await this.yieldControl();
                
            } catch (error) {
                console.error('Error processing chunk:', error);
                this.errors.push({
                    type: 'chunk_error',
                    message: error.message,
                    chunkIndex: Math.floor(i / this.chunkSize)
                });
            }
        }
        
        // Final comprehensive memory cleanup
        console.log(`Streaming File Reader: Completed reading ${results.length} files, performing final cleanup`);

        // Clean up the validFiles array
        this.cleanupChunkMemory(validFiles);

        // Clean up the original fileArray
        this.cleanupChunkMemory(fileArray);

        // Force final garbage collection
        await this.triggerGarbageCollection();

        // Final progress report
        if (progressCallback) {
            progressCallback({
                phase: 'complete',
                processed: this.processedFiles,
                total: this.totalFiles,
                skipped: this.skippedFiles,
                errors: this.errors.length,
                results: results.length,
                memoryUsage: this.memoryMonitor.getUsage()
            });
        }

        console.log(`Streaming File Reader: Final cleanup completed, memory usage:`, this.memoryMonitor.getUsage());

        return results;
    }

    /**
     * Validate files before processing
     * @param {Array} files - Files to validate
     * @returns {Array} - Valid files
     */
    validateFiles(files) {
        const validFiles = [];
        
        for (const file of files) {
            try {
                // Check file size
                if (file.size > this.maxFileSize) {
                    this.skippedFiles++;
                    this.errors.push({
                        type: 'file_too_large',
                        fileName: file.name,
                        size: file.size,
                        maxSize: this.maxFileSize
                    });
                    continue;
                }
                
                // Check file type (basic validation)
                if (!this.isValidFileType(file)) {
                    this.skippedFiles++;
                    this.errors.push({
                        type: 'invalid_file_type',
                        fileName: file.name,
                        type: file.type
                    });
                    continue;
                }
                
                validFiles.push(file);
                
            } catch (error) {
                this.skippedFiles++;
                this.errors.push({
                    type: 'validation_error',
                    fileName: file.name || 'unknown',
                    message: error.message
                });
            }
        }
        
        return validFiles;
    }

    /**
     * Check if file type is valid for processing
     * @param {File} file - File to check
     * @returns {boolean} - True if valid
     */
    isValidFileType(file) {
        const validExtensions = ['.js', '.mjs', '.ts', '.jsx', '.tsx', '.html', '.htm', '.vue', '.svelte', '.json', '.css', '.scss', '.sass', '.less'];
        const fileName = file.name.toLowerCase();
        return validExtensions.some(ext => fileName.endsWith(ext));
    }

    /**
     * Process a chunk of files with concurrency control
     * @param {Array} chunk - Files to process
     * @param {Function} progressCallback - Progress callback
     * @returns {Promise<Array>} - Processed files
     */
    async processChunk(chunk, progressCallback) {
        const results = [];
        const semaphore = new Semaphore(this.maxConcurrentReads);
        
        const promises = chunk.map(async (file) => {
            await semaphore.acquire();
            
            try {
                const result = await this.readFileWithTimeout(file);
                if (result) {
                    results.push(result);
                }
                
                this.processedFiles++;
                
                if (progressCallback && this.processedFiles % 10 === 0) {
                    progressCallback({
                        phase: 'reading',
                        processed: this.processedFiles,
                        total: this.totalFiles,
                        currentFile: file.name,
                        memoryUsage: this.memoryMonitor.getUsage()
                    });
                }
                
            } catch (error) {
                console.error('Error reading file:', file.name, error);
                this.errors.push({
                    type: 'read_error',
                    fileName: file.name,
                    message: error.message
                });
                this.processedFiles++;
                
            } finally {
                semaphore.release();
            }
        });
        
        await Promise.all(promises);
        return results;
    }

    /**
     * Read a single file with timeout protection
     * @param {File} file - File to read
     * @returns {Promise<Object>} - File content object
     */
    async readFileWithTimeout(file) {
        return new Promise((resolve, reject) => {
            const timeout = setTimeout(() => {
                reject(new Error(`File read timeout: ${file.name}`));
            }, this.readTimeout);
            
            const reader = new FileReader();
            
            reader.onload = () => {
                clearTimeout(timeout);
                try {
                    const result = {
                        path: file.webkitRelativePath || file.name,
                        content: reader.result,
                        size: file.size,
                        lastModified: file.lastModified,
                        type: file.type
                    };

                    // Immediately clear the reader event handlers to free memory
                    // Note: reader.result is read-only, so we can't set it to null
                    reader.onload = null;
                    reader.onerror = null;
                    reader.onabort = null;

                    resolve(result);
                } catch (error) {
                    reject(new Error(`Error processing file result: ${error.message}`));
                }
            };
            
            reader.onerror = () => {
                clearTimeout(timeout);

                // Clean up reader event handlers (result is read-only)
                reader.onload = null;
                reader.onerror = null;
                reader.onabort = null;

                reject(new Error(`FileReader error: ${reader.error?.message || 'Unknown error'}`));
            };

            reader.onabort = () => {
                clearTimeout(timeout);

                // Clean up reader event handlers (result is read-only)
                reader.onload = null;
                reader.onerror = null;
                reader.onabort = null;

                reject(new Error(`File read aborted: ${file.name}`));
            };
            
            try {
                reader.readAsText(file);
            } catch (error) {
                clearTimeout(timeout);
                reject(new Error(`Failed to start reading file: ${error.message}`));
            }
        });
    }

    /**
     * Check current memory usage and trigger cleanup if needed
     */
    async checkMemoryUsage() {
        const usage = this.memoryMonitor.getUsage();
        
        if (usage.percentage > this.memoryThreshold) {
            console.warn(`Memory usage high: ${Math.round(usage.percentage * 100)}%`);
            await this.triggerGarbageCollection();
            return false; // Indicate memory pressure
        }
        
        return true; // Memory usage OK
    }

    /**
     * Trigger garbage collection and cleanup
     */
    async triggerGarbageCollection() {
        // Force garbage collection if available
        if (window.gc) {
            try {
                window.gc();
            } catch (error) {
                console.warn('Manual garbage collection failed:', error);
            }
        }
        
        // Yield control to allow cleanup
        await this.yieldControl(50);
    }

    /**
     * Yield control to the event loop
     * @param {number} delay - Delay in milliseconds
     */
    async yieldControl(delay = 0) {
        return new Promise(resolve => setTimeout(resolve, delay));
    }

    /**
     * Clean up memory after processing a chunk of files
     * @param {Array} chunk - The chunk of files that was just processed
     */
    cleanupChunkMemory(chunk) {
        if (!chunk || chunk.length === 0) return;

        // Clear file references from the chunk
        for (let i = 0; i < chunk.length; i++) {
            const file = chunk[i];
            if (file) {
                // Clear any cached content that might exist
                if (file.stream) {
                    try {
                        if (file.stream.cancel) file.stream.cancel();
                        if (file.stream.abort) file.stream.abort();
                    } catch (e) {
                        // Ignore stream cancellation errors
                    }
                    file.stream = null;
                }
                if (file.arrayBuffer) file.arrayBuffer = null;
                if (file.text) file.text = null;
                if (file._content) file._content = null;
                if (file._text) file._text = null;

                // Nullify the reference
                chunk[i] = null;
            }
        }

        // Clear the chunk array
        chunk.length = 0;

        // Force garbage collection if available (but don't wait)
        if (window.gc && Math.random() < 0.1) { // Only 10% of the time to avoid performance impact
            setTimeout(() => window.gc(), 0);
        }
    }

    /**
     * Get processing statistics
     * @returns {Object} - Processing stats
     */
    getStats() {
        return {
            totalFiles: this.totalFiles,
            processedFiles: this.processedFiles,
            skippedFiles: this.skippedFiles,
            errorCount: this.errors.length,
            errors: this.errors,
            memoryUsage: this.memoryMonitor.getUsage(),
            successRate: this.totalFiles > 0 ? (this.processedFiles - this.errors.length) / this.totalFiles : 0
        };
    }
}

/**
 * Semaphore for controlling concurrency
 */
class Semaphore {
    constructor(maxConcurrency) {
        this.maxConcurrency = maxConcurrency;
        this.currentConcurrency = 0;
        this.queue = [];
    }

    async acquire() {
        return new Promise((resolve) => {
            if (this.currentConcurrency < this.maxConcurrency) {
                this.currentConcurrency++;
                resolve();
            } else {
                this.queue.push(resolve);
            }
        });
    }

    release() {
        this.currentConcurrency--;
        if (this.queue.length > 0) {
            const next = this.queue.shift();
            this.currentConcurrency++;
            next();
        }
    }
}

export default StreamingFileReader;