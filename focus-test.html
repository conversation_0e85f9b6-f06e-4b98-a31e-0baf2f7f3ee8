<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Focus Mode Test</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: Arial, sans-serif;
            background-color: #0f1623;
            color: white;
            display: flex;
            flex-direction: column;
            height: 100vh;
        }
        #instructions {
            background-color: #333;
            padding: 15px;
            margin-bottom: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            z-index: 100;
        }
        h1 {
            margin-top: 0;
            color: #4aaeff;
        }
        #visualization-container {
            flex: 1;
            position: relative;
            width: 100%;
            overflow: hidden;
        }
        svg {
            width: 100%;
            height: 100%;
        }
        .selected-node rect {
            stroke: #ff3e00 !important;
            stroke-width: 3px !important;
            filter: drop-shadow(0 0 5px rgba(255, 62, 0, 0.7));
        }
    </style>
</head>
<body>
    <div id="instructions">
        <h1>Focus Mode Test</h1>
        <p><strong>Click on any node</strong> to activate focus mode. Only connections to/from that node will be fully visible.</p>
        <p><strong>Click on the same node again or on the background</strong> to exit focus mode.</p>
    </div>
    <div id="visualization-container">
        <svg id="mindmap-svg"></svg>
    </div>

    <!-- Include the updated mindmap script with cache-busting -->
    <script src="./js/visualizers/mindmap-vanilla.js?v=1.3"></script>
    
    <script>
        // Initialize with test data
        const testData = {
            nodes: [
                { id: 'node1', name: 'File A', path: 'src/fileA.js', imports: [{name:'fileB', path:'src/fileB.js'}, {name:'fileC', path:'src/fileC.js'}], exports: [{name:'funcA'}] },
                { id: 'node2', name: 'File B', path: 'src/fileB.js', imports: [{name:'fileC', path:'src/fileC.js'}], exports: [{name:'funcB'}] },
                { id: 'node3', name: 'File C', path: 'src/fileC.js', imports: [], exports: [{name:'funcC'}] },
                { id: 'node4', name: 'File D', path: 'src/fileD.js', imports: [{name:'fileA', path:'src/fileA.js'}, {name:'fileB', path:'src/fileB.js'}], exports: [{name:'funcD'}] },
                { id: 'node5', name: 'Worker', path: 'src/worker.js', imports: [{name:'fileC', path:'src/fileC.js'}], exports: [] }
            ],
            links: [
                { source: 'node1', target: 'node2', type: 'import' },
                { source: 'node1', target: 'node3', type: 'import' },
                { source: 'node2', target: 'node3', type: 'import' },
                { source: 'node4', target: 'node1', type: 'import' },
                { source: 'node4', target: 'node2', type: 'import' },
                { source: 'node1', target: 'node5', type: 'worker' },
                { source: 'node5', target: 'node3', type: 'import' }
            ]
        };

        // Initialize and render the mindmap
        document.addEventListener('DOMContentLoaded', () => {
            const svg = document.getElementById('mindmap-svg');
            const mindmap = new MindmapProject(svg);
            
            // Update the data with our test data
            mindmap.updateData({
                nodes: testData.nodes.map(node => ({
                    id: node.id,
                    text: node.name,
                    filename: node.path,
                    hasImport: (node.imports && node.imports.length > 0),
                    hasExport: (node.exports && node.exports.length > 0),
                    hasWorker: (node.id === 'node1'), // Only node1 has a worker
                    hasEvent: false
                })),
                links: testData.links
            });

            // Render the initial mindmap
            mindmap.render();
            
            console.log('Focus mode test initialized. Click on any node to see focus mode in action.');
        });
    </script>
</body>
</html>
