/**
 * DOM Utility Functions
 * Helper functions for DOM manipulation and event handling
 */

const DOMUtils = {
    /**
     * Get an element by its ID
     * @param {string} id - Element ID
     * @returns {HTMLElement|null} - The found element or null
     */
    getById(id) {
        return document.getElementById(id);
    },

    /**
     * Create an element with optional attributes and content
     * @param {string} tag - Element tag name
     * @param {Object} attributes - Optional attributes to set
     * @param {string|HTMLElement} content - Optional content to append
     * @returns {HTMLElement} - The created element
     */
    createElement(tag, attributes = {}, content = null) {
        const element = document.createElement(tag);
        
        // Set attributes
        Object.entries(attributes).forEach(([key, value]) => {
            if (key === 'class' || key === 'className') {
                element.className = value;
            } else if (key === 'style' && typeof value === 'object') {
                Object.assign(element.style, value);
            } else {
                element.setAttribute(key, value);
            }
        });
        
        // Set content
        if (content) {
            if (typeof content === 'string') {
                element.innerHTML = content;
            } else if (content instanceof HTMLElement) {
                element.appendChild(content);
            }
        }
        
        return element;
    },

    /**
     * Add event listeners to an element
     * @param {HTMLElement} element - Target element
     * @param {Object} events - Object with event types as keys and handlers as values
     */
    addEventListeners(element, events) {
        Object.entries(events).forEach(([event, handler]) => {
            element.addEventListener(event, handler);
        });
    },

    /**
     * Remove event listeners from an element
     * @param {HTMLElement} element - Target element
     * @param {Object} events - Object with event types as keys and handlers as values
     */
    removeEventListeners(element, events) {
        Object.entries(events).forEach(([event, handler]) => {
            element.removeEventListener(event, handler);
        });
    },

    /**
     * Create and dispatch a custom event
     * @param {string} eventName - Name of the event
     * @param {Object} detail - Event detail data
     * @param {HTMLElement} target - Target element (defaults to document)
     */
    dispatchCustomEvent(eventName, detail = {}, target = document) {
        const event = new CustomEvent(eventName, { detail, bubbles: true });
        target.dispatchEvent(event);
    },

    /**
     * Toggle a class on an element
     * @param {HTMLElement} element - Target element
     * @param {string} className - Class to toggle
     * @param {boolean} force - Optional boolean to force add/remove
     * @returns {boolean} - Current state of the class (true if added)
     */
    toggleClass(element, className, force) {
        return element.classList.toggle(className, force);
    },

    /**
     * Check if an element has a class
     * @param {HTMLElement} element - Target element
     * @param {string} className - Class to check
     * @returns {boolean} - True if element has the class
     */
    hasClass(element, className) {
        return element.classList.contains(className);
    },

    /**
     * Check if a point is inside an element
     * @param {number} clientX - X coordinate
     * @param {number} clientY - Y coordinate
     * @param {HTMLElement} element - Element to check
     * @returns {boolean} - True if point is inside element
     */
    isPointInside(clientX, clientY, element) {
        const rect = element.getBoundingClientRect();
        return (
            clientX >= rect.left &&
            clientX <= rect.right &&
            clientY >= rect.top &&
            clientY <= rect.bottom
        );
    }
};

// Export the utility object
export default DOMUtils;
