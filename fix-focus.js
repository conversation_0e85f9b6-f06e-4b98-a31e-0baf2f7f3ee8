// This file contains fixes for the focus mode functionality
// Copy and paste this code into your browser console when testing focus mode

// Fix worker connections rendering
MindmapProject.prototype.renderWorkerConnections = function(parentElement = this.svgElement) {
    console.log('MindmapProject.renderWorkerConnections - Rendering', this.workerConnections.length, 'worker connections');
    this.workerConnections.forEach((conn, index) => {
        const fromNode = this.nodes.find((n) => n.id === conn.from);
        const toNode = this.nodes.find((n) => n.id === conn.to);
        
        if (!fromNode || !toNode) {
            console.warn('MindmapProject.renderWorkerConnections - Missing nodes for worker connection', index, ':', conn);
            return;
        }
        
        const g = document.createElementNS('http://www.w3.org/2000/svg', 'g');
            
        // Apply focus mode - maintain visibility but highlight relevant connections
        if (this.selectedNodeId && conn.from !== this.selectedNodeId && conn.to !== this.selectedNodeId) {
            g.setAttribute('opacity', '0.4'); // Dimmed but still visible
        } else {
            g.setAttribute('opacity', '1'); // Full visibility for connected paths
            
            // Add a data attribute to identify which node this connection belongs to
            if (conn.from === this.selectedNodeId) {
                g.setAttribute('data-connected-to-selected', 'from');
            } else if (conn.to === this.selectedNodeId) {
                g.setAttribute('data-connected-to-selected', 'to');
            }
        }
        
        // Generate curve with more space for multiple parallel connections
        const {
            path,
            startX,
            startY,
            endX,
            endY
        } = this.getWorkerConnectionPath(conn.from, conn.to);
        
        const pathElement = document.createElementNS('http://www.w3.org/2000/svg', 'path');
        pathElement.setAttribute('d', path);
        pathElement.setAttribute('fill', 'none');
        pathElement.setAttribute('stroke-width', '3');
        pathElement.setAttribute('stroke', 'url(#workerGradient)');
        pathElement.setAttribute('stroke-dasharray', '5,5'); // Larger dashes

        const arrow = document.createElementNS('http://www.w3.org/2000/svg', 'polygon');
        arrow.setAttribute(
            'points',
            `${endX - 6},${endY - 3} ${endX + 2},${endY} ${endX - 6},${endY + 3}`,
        );
        arrow.setAttribute('fill', '#3b82f6');

        g.appendChild(pathElement);
        g.appendChild(arrow);
        
        // Use correct parent element - critical fix for focus mode
        parentElement.appendChild(g);
    });
    console.log('MindmapProject.renderWorkerConnections - Rendered', this.workerConnections.length, 'worker connection paths');
};

// Fix event connections rendering
MindmapProject.prototype.renderEventConnections = function(parentElement = this.svgElement) {
    console.log('MindmapProject.renderEventConnections - Rendering', this.eventConnections.length, 'event connections');
    this.eventConnections.forEach((conn, index) => {
        const fromNode = this.nodes.find((n) => n.id === conn.from);
        const toNode = this.nodes.find((n) => n.id === conn.to);
        
        if (!fromNode || !toNode) {
            console.warn('MindmapProject.renderEventConnections - Missing nodes for event connection', index, ':', conn);
            return;
        }
        
        const g = document.createElementNS('http://www.w3.org/2000/svg', 'g');
        
        // Apply focus mode - maintain visibility but highlight relevant connections
        if (this.selectedNodeId && conn.from !== this.selectedNodeId && conn.to !== this.selectedNodeId) {
            g.setAttribute('opacity', '0.4'); // Dimmed but still visible
        } else {
            g.setAttribute('opacity', '1'); // Full visibility for connected paths
            
            // Add a data attribute to identify which node this connection belongs to
            if (conn.from === this.selectedNodeId) {
                g.setAttribute('data-connected-to-selected', 'from');
            } else if (conn.to === this.selectedNodeId) {
                g.setAttribute('data-connected-to-selected', 'to');
            }
        }
        
        // Generate curve with more space for multiple parallel connections
        const {
            path,
            startX,
            startY,
            endX,
            endY
        } = this.getEventConnectionPath(conn.from, conn.to);
        
        const pathElement = document.createElementNS('http://www.w3.org/2000/svg', 'path');
        pathElement.setAttribute('d', path);
        pathElement.setAttribute('fill', 'none');
        pathElement.setAttribute('stroke-width', '3');
        pathElement.setAttribute('stroke', 'url(#eventGradient)');
        pathElement.setAttribute('stroke-dasharray', '3,2');

        const arrow = document.createElementNS('http://www.w3.org/2000/svg', 'polygon');
        arrow.setAttribute(
            'points',
            `${endX - 6},${endY - 3} ${endX + 2},${endY} ${endX - 6},${endY + 3}`,
        );
        arrow.setAttribute('fill', '#ec4899'); // Pink for events

        g.appendChild(pathElement);
        g.appendChild(arrow);
        
        // Use correct parent element - critical fix for focus mode
        parentElement.appendChild(g);
    });
    console.log('MindmapProject.renderEventConnections - Rendered', this.eventConnections.length, 'event connection paths');
};

// Fix node click handling
MindmapProject.prototype.handleNodeClick = function(e, nodeId) {
    console.log('Node clicked:', nodeId);
    
    // Toggle selection - if clicking the same node, clear selection
    if (this.selectedNodeId === nodeId) {
        this.selectedNodeId = null;
    } else {
        this.selectedNodeId = nodeId;
    }
    
    // Re-render to apply focus effect
    this.render();
    
    // Stop propagation to prevent other handlers
    e.stopPropagation();
    e.preventDefault();
};

console.log('Focus mode fixes loaded. Reload the page to apply them.');
