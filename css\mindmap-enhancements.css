/* Enhanced Mindmap Styles */
.dependency-mindmap {
    background: var(--graph-bg, #2d2d2d);
    border-radius: 8px;
}

/* Node Tooltip Styles */
.node-tooltip {
    position: absolute;
    background: rgba(0, 0, 0, 0.9);
    color: white;
    padding: 12px;
    border-radius: 6px;
    font-size: 12px;
    max-width: 300px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
    z-index: 1000;
    opacity: 0;
    pointer-events: none;
    transition: opacity 0.2s ease;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.node-tooltip.visible {
    opacity: 1;
}

.tooltip-title {
    font-weight: bold;
    font-size: 14px;
    margin-bottom: 8px;
    color: #fff;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    padding-bottom: 4px;
}

.tooltip-path {
    font-size: 11px;
    color: #ccc;
    margin-bottom: 8px;
    word-break: break-all;
}

.tooltip-stats {
    display: grid;
    gap: 4px;
}

.stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.stat-label {
    color: #aaa;
    font-size: 11px;
}

.stat-value {
    color: #fff;
    font-weight: 500;
    font-size: 11px;
}

/* Node Styles */
.mindmap-container .node {
    cursor: pointer;
}

.mindmap-container .node rect {
    transition: all 0.2s ease;
}

.mindmap-container .node:hover rect {
    stroke-width: 2px !important;
    filter: brightness(1.1);
}

.mindmap-container .node text {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    font-size: 11px;
    font-weight: 500;
    fill: white;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
    pointer-events: none;
}

/* Link Styles */
.mindmap-container .link {
    transition: all 0.2s ease;
}

/* Toggle Button Styles */
.node-toggle {
    font-family: monospace;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.2s ease;
}

.node-toggle:hover {
    fill: #ffeb3b !important;
    font-size: 12px !important;
}

/* Risk Level Colors */
.risk-high {
    background-color: #ff6b6b;
}

.risk-medium {
    background-color: #ffa726;
}

.risk-low {
    background-color: #66bb6a;
}

/* Category Colors */
.category-entry {
    background-color: #42a5f5;
}

.category-config {
    background-color: #ab47bc;
}

.category-utility {
    background-color: #66bb6a;
}

.category-component {
    background-color: #26c6da;
}

.category-service {
    background-color: #ffca28;
}

.category-test {
    background-color: #78909c;
}

/* Loading and Message Styles */
.mindmap-message {
    position: absolute;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 10px 20px;
    border-radius: 5px;
    z-index: 1000;
    font-size: 14px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

/* Responsive Design */
@media (max-width: 768px) {
    .node-tooltip {
        max-width: 250px;
        font-size: 11px;
    }
    
    .tooltip-title {
        font-size: 13px;
    }
    
    .mindmap-container .node text {
        font-size: 10px;
    }
}