:root {
    /* Google AI Studio-inspired color palette */
    --google-background: #0F1419; /* Main background */
    --google-surface: #1E1F24;    /* Sidebar, main content area background */
    --google-surface-variant: #292A30; /* Input fields, secondary elements */
    --google-card-bg: #202124; /* Card/panel backgrounds */
    --google-panel-header: #27282A; /* Card/panel header backgrounds */
    --google-primary: #8AB4F8;    /* Accent blue for text, icons, active states */
    --google-primary-variant: #669DF6; /* Darker blue for hover/active */
    --google-secondary: #81C995; /* Green, for things like success */
    --google-success: #81C995;
    --google-warning: #FDD663;
    --google-error: #F28B82;
    --google-on-surface: rgba(255, 255, 255, 0.87); /* Primary text */
    --google-on-surface-medium: rgba(255, 255, 255, 0.60); /* Secondary text */
    --google-on-surface-disabled: rgba(255, 255, 255, 0.38);
    --google-on-primary: #202124; /* Text on primary-colored buttons */
    --google-divider: rgba(255, 255, 255, 0.12); /* Borders, dividers */
    --google-border: rgba(255, 255, 255, 0.16); /* Slightly more prominent border */
    --google-hover-overlay: rgba(255, 255, 255, 0.05); /* Hover on list items */
    --google-active-overlay: rgba(138, 180, 248, 0.08); /* Active state for menu items */

    /* Core measurements */
    --app-header-height: 56px;
    --sidebar-width: 260px;
    --right-panel-width: 380px; /* Increased slightly for better fit */
    --panel-header-height: 48px;
    --panel-spacing: 16px;
    --radius-sm: 4px;
    --radius-md: 8px;
    --radius-lg: 12px;
    --button-height: 36px;
    --button-pill-radius: 18px; /* For pill-shaped buttons */

    /* Spacing */
    --spacing-xs: 4px;
    --spacing-sm: 8px;
    --spacing-md: 12px;
    --spacing-lg: 16px;
    --spacing-xl: 24px;

    /* Elevation */
    --elevation-1: 0 1px 2px rgba(0, 0, 0, 0.3), 0 1px 3px 1px rgba(0, 0, 0, 0.15);
    --elevation-2: 0 2px 6px rgba(0,0,0,0.15), 0 1px 3px 1px rgba(0,0,0,0.3); /* For sliding panel */

    /* Typography */
    --font-family-default: 'Google Sans', 'Roboto', 'Arial', sans-serif;
    --font-family-mono: 'Roboto Mono', monospace;
    --font-size-xs: 11px;
    --font-size-sm: 12px;
    --font-size-md: 14px;
    --font-size-lg: 16px;
    --font-size-xl: 18px;
    --font-weight-regular: 400;
    --font-weight-medium: 500;
}
