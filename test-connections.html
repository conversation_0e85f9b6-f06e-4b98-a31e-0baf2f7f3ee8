<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Connections</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: #1f2937;
            color: white;
            font-family: Arial, sans-serif;
        }
        #visualization-container {
            width: 100%;
            height: 600px;
            border: 1px solid #374151;
            border-radius: 8px;
            background: #1f2937;
        }
        #debug-info {
            margin-top: 20px;
            padding: 10px;
            background: #374151;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <h1>Connection Rendering Test</h1>
    <div id="visualization-container"></div>
    <div id="debug-info">
        <h3>Debug Information:</h3>
        <div id="debug-output"></div>
    </div>

    <script src="js/visualizers/mindmap-vanilla.js"></script>
    <script>
        // Test data with nodes and connections
        const testData = {
            nodes: [
                {
                    id: 'node1',
                    name: 'File A',
                    path: 'src/fileA.js',
                    imports: [{ name: 'fileB', path: 'src/fileB.js' }],
                    exports: [{ name: 'functionA' }],
                    functions: [{ name: 'functionA' }]
                },
                {
                    id: 'node2', 
                    name: 'File B',
                    path: 'src/fileB.js',
                    imports: [],
                    exports: [{ name: 'functionB' }],
                    functions: [{ name: 'functionB' }]
                },
                {
                    id: 'node3',
                    name: 'File C', 
                    path: 'src/fileC.js',
                    imports: [{ name: 'fileA', path: 'src/fileA.js' }],
                    exports: [{ name: 'functionC' }],
                    functions: [{ name: 'functionC' }]
                }
            ],
            links: [
                { source: 'node1', target: 'node2', type: 'import' },
                { source: 'node3', target: 'node1', type: 'import' },
                { source: 'node1', target: 'node3', type: 'worker' }
            ]
        };

        function log(message) {
            const debugOutput = document.getElementById('debug-output');
            debugOutput.innerHTML += '<div>' + new Date().toLocaleTimeString() + ': ' + message + '</div>';
            console.log(message);
        }

        // Initialize the mindmap
        try {
            log('Starting mindmap initialization...');
            
            const container = document.getElementById('visualization-container');
            const svg = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
            svg.setAttribute('width', '100%');
            svg.setAttribute('height', '100%');
            svg.setAttribute('viewBox', '0 0 1100 500');
            svg.style.background = '#1f2937';
            container.appendChild(svg);
            
            log('SVG element created and added to container');
            
            // Create mindmap instance
            const mindmap = new MindmapProject(svg, testData);
            log('MindmapProject instance created with test data');
            
            // Force render
            mindmap.render();
            log('Render method called');
            
            // Log final state
            setTimeout(() => {
                log('Final state - Nodes: ' + mindmap.nodes.length + 
                    ', Connections: ' + mindmap.connections.length +
                    ', Worker Connections: ' + mindmap.workerConnections.length +
                    ', Event Connections: ' + mindmap.eventConnections.length);
                    
                // Check if SVG has any path elements (connections)
                const paths = svg.querySelectorAll('path');
                log('SVG contains ' + paths.length + ' path elements');
                
                const lines = svg.querySelectorAll('line');
                log('SVG contains ' + lines.length + ' line elements');
                
                const groups = svg.querySelectorAll('g');
                log('SVG contains ' + groups.length + ' group elements');
            }, 1000);
            
        } catch (error) {
            log('Error: ' + error.message);
            console.error('Error:', error);
        }
    </script>
</body>
</html>