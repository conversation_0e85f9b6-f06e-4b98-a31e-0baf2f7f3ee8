/* Panel styles */
#details-panel-container {
    position: fixed;
    top: 0;
    right: -400px; /* Start off-screen */
    height: 100vh;
    width: 400px;
    background-color: rgb(30, 32, 35);
    box-shadow: -3px 0 15px rgba(0, 0, 0, 0.5);
    transition: right 0.3s ease;
    z-index: 1000;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    border-left: 1px solid rgba(255, 255, 255, 0.08);
    pointer-events: all !important; /* Force enable pointer events */
}

#details-panel-container.open {
    right: 0; /* Slide in */
}

.details-panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    background-color: var(--google-panel-header);
    border-bottom: 1px solid var(--google-divider);
    pointer-events: auto; /* Enable pointer events for header */
}

.details-panel-header h3 {
    margin: 0;
    font-size: 16px;
    font-weight: 500;
}

.close-button {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    pointer-events: auto; /* Enable pointer events for close button */
    color: var(--google-on-surface-medium);
    line-height: 1;
}

.close-button:hover {
    color: var(--google-on-surface);
}

.details-panel-content {
    flex: 1;
    overflow-y: auto;
    padding: 16px;
    pointer-events: auto; /* Enable pointer events for panel content */
    z-index: 1001; /* Ensure panel content is above canvas */
}

.details-section {
    margin-bottom: 20px;
    background-color: rgba(40, 42, 48, 0.8);
    border-radius: 6px;
    overflow: hidden;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
}

.details-section ul {
    list-style: none;
    padding: 8px 0;
    margin: 0;
}

.details-section li {
    margin-bottom: 0;
    padding: 8px 16px;
    font-size: 13px;
    color: rgba(255, 255, 255, 0.85);
    transition: background-color 0.2s;
    display: flex;
    align-items: center;
    cursor: pointer;
}

.details-section li:hover {
    background-color: rgba(255, 255, 255, 0.05);
}

.details-section li::before {
    content: "";
    display: inline-block;
    width: 6px;
    height: 6px;
    border-radius: 50%;
    margin-right: 8px;
    background-color: rgba(255, 255, 255, 0.4);
}

/* Base style for all section titles */
.section-title, .details-section h3 {
    font-size: 13px;
    font-weight: 600;
    margin-bottom: 0;
    padding: 8px 12px;
    /* Default style for dark theme */
    color: rgba(255, 255, 255, 0.9);
    background-color: rgba(255, 255, 255, 0.05);
    border-bottom: 1px solid rgba(255, 255, 255, 0.08);
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.section-title::after, .details-section h3::after {
    content: '▼';
    font-size: 10px;
    transition: transform 0.2s ease;
}

/* Content area transition */
.content-scroll-area {
    transition: margin-right 0.3s ease;
    margin-right: 0;
}

.content-scroll-area.panel-open {
    margin-right: var(--right-panel-width);
}
