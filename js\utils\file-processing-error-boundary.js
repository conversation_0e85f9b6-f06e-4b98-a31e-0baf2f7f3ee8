/**
 * File Processing Error Boundary
 * Provides comprehensive error handling and recovery for file processing operations
 */

class FileProcessingErrorBoundary {
    constructor(options = {}) {
        this.maxRetries = options.maxRetries || 3;
        this.retryDelay = options.retryDelay || 1000;
        this.timeoutDuration = options.timeoutDuration || 30000;
        this.enableRecovery = options.enableRecovery !== false;
        
        // Error handlers for different error types
        this.errorHandlers = new Map();
        this.retryStrategies = new Map();
        this.recoveryStrategies = new Map();
        
        // Statistics
        this.stats = {
            totalOperations: 0,
            successfulOperations: 0,
            failedOperations: 0,
            retriedOperations: 0,
            recoveredOperations: 0,
            errorsByType: new Map()
        };
        
        // Initialize default handlers
        this.initializeDefaultHandlers();
        
        // Bind methods
        this.safeProcess = this.safeProcess.bind(this);
        this.handleError = this.handleError.bind(this);
        this.retry = this.retry.bind(this);
    }

    /**
     * Initialize default error handlers and strategies
     */
    initializeDefaultHandlers() {
        // Permission errors
        this.registerErrorHandler('PermissionError', (error, context) => {
            console.warn(`Permission denied for ${context.filePath || 'unknown file'}`);
            return {
                skip: true,
                reason: 'Permission denied',
                recoverable: false
            };
        });

        // File not found errors
        this.registerErrorHandler('FileNotFoundError', (error, context) => {
            console.warn(`File not found: ${context.filePath || 'unknown file'}`);
            return {
                skip: true,
                reason: 'File not found',
                recoverable: false
            };
        });

        // Network timeout errors
        this.registerErrorHandler('NetworkTimeoutError', (error, context) => {
            console.warn(`Network timeout for ${context.filePath || 'unknown file'}`);
            return {
                retry: true,
                maxRetries: 3,
                delay: 2000,
                reason: 'Network timeout'
            };
        });

        // Memory errors
        this.registerErrorHandler('MemoryError', (error, context) => {
            console.error(`Memory error processing ${context.filePath || 'unknown file'}`);
            return {
                skip: true,
                reason: 'Insufficient memory',
                recoverable: true,
                recovery: 'reduce_load'
            };
        });

        // Corruption errors
        this.registerErrorHandler('CorruptionError', (error, context) => {
            console.warn(`Corrupted file detected: ${context.filePath || 'unknown file'}`);
            return {
                skip: true,
                reason: 'File corruption detected',
                recoverable: false
            };
        });

        // Timeout errors
        this.registerErrorHandler('TimeoutError', (error, context) => {
            console.warn(`Operation timeout for ${context.filePath || 'unknown file'}`);
            return {
                retry: true,
                maxRetries: 2,
                delay: 1000,
                reason: 'Operation timeout'
            };
        });

        // Generic errors
        this.registerErrorHandler('GenericError', (error, context) => {
            console.error(`Generic error processing ${context.filePath || 'unknown file'}:`, error);
            return {
                retry: true,
                maxRetries: 1,
                delay: 500,
                reason: 'Generic processing error'
            };
        });

        // Recovery strategies
        this.registerRecoveryStrategy('reduce_load', async (context) => {
            // Trigger garbage collection
            if (window.gc) {
                window.gc();
            }
            
            // Wait for memory to stabilize
            await this.delay(1000);
            
            // Reduce processing load
            if (context.chunkSize) {
                context.chunkSize = Math.max(10, Math.floor(context.chunkSize / 2));
            }
            
            return { success: true, message: 'Reduced processing load' };
        });

        this.registerRecoveryStrategy('clear_cache', async (context) => {
            // Clear any cached data
            if (context.cache) {
                context.cache.clear();
            }
            
            return { success: true, message: 'Cleared cache' };
        });
    }

    /**
     * Register an error handler for a specific error type
     * @param {string} errorType - Type of error
     * @param {Function} handler - Handler function
     */
    registerErrorHandler(errorType, handler) {
        this.errorHandlers.set(errorType, handler);
    }

    /**
     * Register a recovery strategy
     * @param {string} strategyName - Name of the strategy
     * @param {Function} strategy - Strategy function
     */
    registerRecoveryStrategy(strategyName, strategy) {
        this.recoveryStrategies.set(strategyName, strategy);
    }

    /**
     * Safely process an operation with comprehensive error handling
     * @param {Function} operation - Operation to execute
     * @param {Object} context - Operation context
     * @returns {Promise<Object>} - Operation result
     */
    async safeProcess(operation, context = {}) {
        this.stats.totalOperations++;
        
        const operationId = `op_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        const startTime = Date.now();
        
        try {
            // Add timeout protection
            const result = await Promise.race([
                this.executeOperation(operation, context),
                this.createTimeoutPromise(context.timeout || this.timeoutDuration, context)
            ]);
            
            this.stats.successfulOperations++;
            
            return {
                success: true,
                result,
                operationId,
                duration: Date.now() - startTime,
                context
            };
            
        } catch (error) {
            return await this.handleError(error, context, operation, operationId, startTime);
        }
    }

    /**
     * Execute operation with validation
     * @param {Function} operation - Operation to execute
     * @param {Object} context - Operation context
     * @returns {Promise<any>} - Operation result
     */
    async executeOperation(operation, context) {
        // Validate operation
        if (typeof operation !== 'function') {
            throw new Error('Operation must be a function');
        }
        
        // Pre-execution validation
        await this.validateContext(context);
        
        // Execute operation
        return await operation(context);
    }

    /**
     * Validate operation context
     * @param {Object} context - Context to validate
     */
    async validateContext(context) {
        // Check file path if provided
        if (context.filePath) {
            await this.validateFilePath(context.filePath);
        }
        
        // Check memory availability
        if (context.requiresMemory) {
            await this.validateMemoryAvailability(context.requiresMemory);
        }
        
        // Check network connectivity if needed
        if (context.requiresNetwork) {
            await this.validateNetworkConnectivity();
        }
    }

    /**
     * Validate file path accessibility
     * @param {string} filePath - File path to validate
     */
    async validateFilePath(filePath) {
        // Basic path validation
        if (!filePath || typeof filePath !== 'string') {
            const error = new Error('Invalid file path');
            error.name = 'FileNotFoundError';
            throw error;
        }
        
        // Check for suspicious patterns that might indicate corruption
        if (this.detectSuspiciousPath(filePath)) {
            const error = new Error('Suspicious file path detected');
            error.name = 'CorruptionError';
            throw error;
        }
    }

    /**
     * Detect suspicious file paths
     * @param {string} filePath - File path to check
     * @returns {boolean} - True if suspicious
     */
    detectSuspiciousPath(filePath) {
        const suspiciousPatterns = [
            /\0/,           // Null bytes
            /[<>:"|?*]/,    // Invalid filename characters
            /\.\.[\\/]/,    // Directory traversal
            /^[\\/]/,       // Absolute paths (in some contexts)
        ];
        
        return suspiciousPatterns.some(pattern => pattern.test(filePath));
    }

    /**
     * Validate memory availability
     * @param {number} requiredMemory - Required memory in bytes
     */
    async validateMemoryAvailability(requiredMemory) {
        if (performance.memory) {
            const available = performance.memory.jsHeapSizeLimit - performance.memory.usedJSHeapSize;
            
            if (available < requiredMemory) {
                const error = new Error(`Insufficient memory: ${available} < ${requiredMemory}`);
                error.name = 'MemoryError';
                throw error;
            }
        }
    }

    /**
     * Validate network connectivity
     */
    async validateNetworkConnectivity() {
        if (!navigator.onLine) {
            const error = new Error('Network connectivity required but not available');
            error.name = 'NetworkTimeoutError';
            throw error;
        }
    }

    /**
     * Handle errors with appropriate strategies
     * @param {Error} error - Error that occurred
     * @param {Object} context - Operation context
     * @param {Function} operation - Original operation
     * @param {string} operationId - Operation ID
     * @param {number} startTime - Start time
     * @returns {Promise<Object>} - Error handling result
     */
    async handleError(error, context, operation, operationId, startTime) {
        this.stats.failedOperations++;
        
        // Classify error
        const errorType = this.classifyError(error);
        this.updateErrorStats(errorType);
        
        // Get error handling strategy
        const strategy = this.getErrorStrategy(errorType, error, context);
        
        console.log(`Handling ${errorType} for operation ${operationId}:`, strategy);
        
        // Apply recovery if needed
        if (strategy.recovery && this.enableRecovery) {
            try {
                const recoveryResult = await this.applyRecovery(strategy.recovery, context);
                if (recoveryResult.success) {
                    this.stats.recoveredOperations++;
                    console.log(`Recovery successful: ${recoveryResult.message}`);
                }
            } catch (recoveryError) {
                console.error('Recovery failed:', recoveryError);
            }
        }
        
        // Retry if strategy suggests it
        if (strategy.retry && context.retryCount < (strategy.maxRetries || this.maxRetries)) {
            return await this.retry(operation, context, strategy, operationId, startTime);
        }
        
        // Skip if strategy suggests it
        if (strategy.skip) {
            return {
                success: false,
                skipped: true,
                reason: strategy.reason || 'Operation skipped due to error',
                error: this.sanitizeError(error),
                operationId,
                duration: Date.now() - startTime,
                context
            };
        }
        
        // Default: return error
        return {
            success: false,
            error: this.sanitizeError(error),
            errorType,
            strategy,
            operationId,
            duration: Date.now() - startTime,
            context
        };
    }

    /**
     * Classify error type
     * @param {Error} error - Error to classify
     * @returns {string} - Error type
     */
    classifyError(error) {
        if (!error) return 'GenericError';
        
        const errorName = error.name || '';
        const errorMessage = error.message || '';
        
        // Check for specific error types
        if (errorName === 'TimeoutError' || errorMessage.includes('timeout')) {
            return 'TimeoutError';
        }
        
        if (errorMessage.includes('permission') || errorMessage.includes('access denied')) {
            return 'PermissionError';
        }
        
        if (errorMessage.includes('not found') || errorMessage.includes('ENOENT')) {
            return 'FileNotFoundError';
        }
        
        if (errorMessage.includes('network') || errorMessage.includes('fetch')) {
            return 'NetworkTimeoutError';
        }
        
        if (errorMessage.includes('memory') || errorMessage.includes('heap')) {
            return 'MemoryError';
        }
        
        if (errorMessage.includes('corrupt') || errorMessage.includes('invalid')) {
            return 'CorruptionError';
        }
        
        return 'GenericError';
    }

    /**
     * Get error handling strategy
     * @param {string} errorType - Type of error
     * @param {Error} error - Error object
     * @param {Object} context - Operation context
     * @returns {Object} - Error handling strategy
     */
    getErrorStrategy(errorType, error, context) {
        const handler = this.errorHandlers.get(errorType) || this.errorHandlers.get('GenericError');
        return handler(error, context);
    }

    /**
     * Apply recovery strategy
     * @param {string} recoveryName - Name of recovery strategy
     * @param {Object} context - Operation context
     * @returns {Promise<Object>} - Recovery result
     */
    async applyRecovery(recoveryName, context) {
        const strategy = this.recoveryStrategies.get(recoveryName);
        
        if (!strategy) {
            return { success: false, message: `Unknown recovery strategy: ${recoveryName}` };
        }
        
        try {
            return await strategy(context);
        } catch (error) {
            return { success: false, message: `Recovery failed: ${error.message}` };
        }
    }

    /**
     * Retry operation with backoff
     * @param {Function} operation - Operation to retry
     * @param {Object} context - Operation context
     * @param {Object} strategy - Retry strategy
     * @param {string} operationId - Operation ID
     * @param {number} startTime - Original start time
     * @returns {Promise<Object>} - Retry result
     */
    async retry(operation, context, strategy, operationId, startTime) {
        this.stats.retriedOperations++;
        
        const retryCount = (context.retryCount || 0) + 1;
        const delay = strategy.delay || this.retryDelay;
        const backoffDelay = delay * Math.pow(2, retryCount - 1); // Exponential backoff
        
        console.log(`Retrying operation ${operationId} (attempt ${retryCount}) after ${backoffDelay}ms`);
        
        // Wait before retry
        await this.delay(backoffDelay);
        
        // Update context for retry
        const retryContext = {
            ...context,
            retryCount,
            isRetry: true,
            originalError: strategy.reason
        };
        
        // Retry the operation
        return await this.safeProcess(operation, retryContext);
    }

    /**
     * Create timeout promise
     * @param {number} timeout - Timeout duration
     * @param {Object} context - Operation context
     * @returns {Promise} - Promise that rejects on timeout
     */
    createTimeoutPromise(timeout, context) {
        return new Promise((_, reject) => {
            setTimeout(() => {
                const error = new Error(`Operation timeout after ${timeout}ms`);
                error.name = 'TimeoutError';
                error.context = context;
                reject(error);
            }, timeout);
        });
    }

    /**
     * Sanitize error for safe serialization
     * @param {Error} error - Error to sanitize
     * @returns {Object} - Sanitized error
     */
    sanitizeError(error) {
        return {
            name: error.name || 'Error',
            message: error.message || 'Unknown error',
            stack: error.stack ? error.stack.split('\n').slice(0, 5).join('\n') : undefined,
            code: error.code,
            errno: error.errno
        };
    }

    /**
     * Update error statistics
     * @param {string} errorType - Type of error
     */
    updateErrorStats(errorType) {
        const current = this.stats.errorsByType.get(errorType) || 0;
        this.stats.errorsByType.set(errorType, current + 1);
    }

    /**
     * Utility delay function
     * @param {number} ms - Milliseconds to delay
     */
    async delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    /**
     * Get processing statistics
     * @returns {Object} - Processing statistics
     */
    getStats() {
        return {
            ...this.stats,
            errorsByType: Object.fromEntries(this.stats.errorsByType),
            successRate: this.stats.totalOperations > 0 
                ? this.stats.successfulOperations / this.stats.totalOperations 
                : 0,
            retryRate: this.stats.totalOperations > 0 
                ? this.stats.retriedOperations / this.stats.totalOperations 
                : 0,
            recoveryRate: this.stats.failedOperations > 0 
                ? this.stats.recoveredOperations / this.stats.failedOperations 
                : 0
        };
    }

    /**
     * Reset statistics
     */
    resetStats() {
        this.stats = {
            totalOperations: 0,
            successfulOperations: 0,
            failedOperations: 0,
            retriedOperations: 0,
            recoveredOperations: 0,
            errorsByType: new Map()
        };
    }

    /**
     * Create a safe wrapper for any async function
     * @param {Function} fn - Function to wrap
     * @param {Object} defaultContext - Default context
     * @returns {Function} - Wrapped function
     */
    createSafeWrapper(fn, defaultContext = {}) {
        return async (...args) => {
            const context = { ...defaultContext, args };
            return await this.safeProcess(() => fn(...args), context);
        };
    }
}

export default FileProcessingErrorBoundary;