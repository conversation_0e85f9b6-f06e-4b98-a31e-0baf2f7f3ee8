/* Line Visibility Controller Styles */

.line-visibility-panel {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.line-visibility-panel::-webkit-scrollbar {
    width: 6px;
}

.line-visibility-panel::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.2);
    border-radius: 3px;
}

.line-visibility-panel::-webkit-scrollbar-thumb {
    background: rgba(59, 130, 246, 0.5);
    border-radius: 3px;
}

.line-visibility-panel::-webkit-scrollbar-thumb:hover {
    background: rgba(59, 130, 246, 0.7);
}

/* Panel animations */
.line-visibility-panel {
    opacity: 0;
    transform: translateY(-10px);
}

.line-visibility-panel.visible {
    opacity: 1;
    transform: translateY(0);
}

/* Line type sections */
.line-type-section {
    transition: all 0.2s ease;
}

.line-type-section:hover {
    background: rgba(0, 0, 0, 0.3) !important;
}

/* Line items */
.line-item {
    cursor: pointer;
}

.line-item:hover {
    transform: translateX(2px);
}

/* Toggle switches */
.line-toggle {
    position: relative;
    outline: none;
}

.line-toggle:focus {
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.5);
}

.line-toggle:active {
    transform: scale(0.95);
}

/* Global control buttons */
.global-controls button:active {
    transform: scale(0.95);
}

/* Close button hover effects */
.close-panel-btn:hover {
    transform: scale(1.1);
}

/* Line highlighting effects */
.line-highlight {
    filter: drop-shadow(0 0 6px rgba(59, 130, 246, 0.8));
    stroke-width: 4 !important;
    z-index: 1000;
}

/* Connection type indicators */
.connection-type-indicator {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 6px;
}

.connection-type-indicator.regular {
    background: #9ca3af;
}

.connection-type-indicator.worker {
    background: #3b82f6;
}

.connection-type-indicator.event {
    background: #eab308;
}

/* Responsive design */
@media (max-width: 768px) {
    .line-visibility-panel {
        width: 260px !important;
        max-height: 350px !important;
        font-size: 14px;
    }
    
    .line-item {
        padding: 6px !important;
    }
    
    .global-controls {
        flex-direction: column !important;
    }
    
    .global-controls button {
        font-size: 11px !important;
        padding: 4px 8px !important;
    }
}

/* Dark theme compatibility */
@media (prefers-color-scheme: dark) {
    .line-visibility-panel {
        background: rgba(17, 24, 39, 0.95) !important;
        border-color: rgba(75, 85, 99, 0.3) !important;
    }
}

/* Light theme compatibility */
.light-theme-active .line-visibility-panel {
    background: rgba(255, 255, 255, 0.95) !important;
    border-color: rgba(59, 130, 246, 0.3) !important;
    color: #1f2937 !important;
}

.light-theme-active .line-visibility-panel h3 {
    color: #1f2937 !important;
}

.light-theme-active .line-item {
    background: rgba(0, 0, 0, 0.05) !important;
    color: #374151 !important;
}

.light-theme-active .line-item:hover {
    background: rgba(0, 0, 0, 0.1) !important;
}

.light-theme-active .line-type-section {
    background: rgba(0, 0, 0, 0.05) !important;
}

.light-theme-active .line-type-section:hover {
    background: rgba(0, 0, 0, 0.1) !important;
}

/* Accessibility improvements */
.line-toggle:focus-visible {
    outline: 2px solid #3b82f6;
    outline-offset: 2px;
}

.close-panel-btn:focus-visible {
    outline: 2px solid #ef4444;
    outline-offset: 2px;
}

/* Animation for line visibility changes */
.line-fade-transition {
    transition: opacity 0.3s ease, filter 0.3s ease, transform 0.3s ease;
}

/* Status indicators */
.visibility-status {
    font-size: 10px;
    padding: 2px 6px;
    border-radius: 10px;
    background: rgba(34, 197, 94, 0.2);
    color: #22c55e;
    margin-left: 8px;
}

.visibility-status.hidden {
    background: rgba(239, 68, 68, 0.2);
    color: #ef4444;
}

/* Loading state */
.line-visibility-panel.loading {
    opacity: 0.6;
    pointer-events: none;
}

.line-visibility-panel.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid transparent;
    border-top: 2px solid #3b82f6;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Tooltip styles */
.line-tooltip {
    position: absolute;
    background: rgba(0, 0, 0, 0.9);
    color: white;
    padding: 8px 12px;
    border-radius: 4px;
    font-size: 12px;
    pointer-events: none;
    z-index: 10000;
    opacity: 0;
    transition: opacity 0.2s ease;
    max-width: 200px;
    word-wrap: break-word;
}

.line-tooltip.visible {
    opacity: 1;
}

/* Compact mode for smaller screens */
.line-visibility-panel.compact {
    width: 240px !important;
    padding: 12px !important;
}

.line-visibility-panel.compact .line-item {
    padding: 4px 6px !important;
    font-size: 11px !important;
}

.line-visibility-panel.compact .global-controls button {
    padding: 4px 6px !important;
    font-size: 10px !important;
}