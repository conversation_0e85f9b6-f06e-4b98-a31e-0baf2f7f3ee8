// Apply this in the browser console to fix the focus mode

// Improved node click handling to reliably detect clicks
MindmapProject.prototype.handleNodeClick = function(e, nodeId) {
    console.log('Node clicked:', nodeId);
    
    // Make sure the drag state doesn't interfere
    if (this.dragState && (this.dragState.isDragging || 
        (this.dragState.moved && Math.abs(this.dragState.moved.x) + Math.abs(this.dragState.moved.y) > 3))) {
        console.log('Ignoring click because node was being dragged');
        return;
    }
    
    // Toggle selection - if clicking the same node, clear selection
    if (this.selectedNodeId === nodeId) {
        console.log('Deselecting node:', nodeId);
        this.selectedNodeId = null;
    } else {
        console.log('Selecting node:', nodeId);
        this.selectedNodeId = nodeId;
    }
    
    // Re-render to apply focus effect
    this.render();
    
    // Stop propagation to prevent other handlers
    e.stopPropagation();
};

// Ensure layers are created correctly in the render method
const originalRender = MindmapProject.prototype.render;
MindmapProject.prototype.render = function() {
    console.log("Enhanced render method called");
    
    if (!this.svgElement) {
        console.warn('MindmapProject.render - No SVG element available');
        return;
    }
    
    // Clear existing content
    this.svgElement.innerHTML = '';
    
    // Create gradients
    this.createGradients();
    
    // Create proper layering
    const backgroundGroup = document.createElementNS('http://www.w3.org/2000/svg', 'g');
    backgroundGroup.setAttribute('class', 'layer-background');
    
    const connectionsGroup = document.createElementNS('http://www.w3.org/2000/svg', 'g');
    connectionsGroup.setAttribute('class', 'layer-connections');
    
    const nodesGroup = document.createElementNS('http://www.w3.org/2000/svg', 'g');
    nodesGroup.setAttribute('class', 'layer-nodes');
    
    // Add the groups to the SVG in the correct order (background at bottom, nodes at top)
    this.svgElement.appendChild(backgroundGroup);
    this.svgElement.appendChild(connectionsGroup);
    this.svgElement.appendChild(nodesGroup);
    
    // Add a background rect to capture clicks outside nodes for clearing selection
    const backgroundRect = document.createElementNS('http://www.w3.org/2000/svg', 'rect');
    backgroundRect.setAttribute('width', '100%');
    backgroundRect.setAttribute('height', '100%');
    backgroundRect.setAttribute('fill', 'transparent');
    backgroundRect.addEventListener('click', () => {
        if (this.selectedNodeId) {
            console.log('Background clicked, clearing selection');
            this.selectedNodeId = null;
            this.render();
        }
    });
    backgroundGroup.appendChild(backgroundRect);
    
    // Render connections in the connections layer
    this.renderConnections(connectionsGroup);
    this.renderWorkerConnections(connectionsGroup);
    this.renderEventConnections(connectionsGroup);
    this.renderArrows(connectionsGroup);
    
    // Render nodes in the nodes layer
    this.renderNodes(nodesGroup);
    
    // Render minimap if enabled
    if (this.minimapEnabled && this.minimapSvg) {
        this.renderMinimap();
    }
};

console.log("Focus mode fix loaded. Apply by refreshing the page.");
