/**
 * Memory Manager
 * Provides proactive memory management and monitoring for large-scale operations
 */

class MemoryManager {
    constructor(options = {}) {
        this.memoryThreshold = options.memoryThreshold || 0.8; // 80% of available memory
        this.criticalThreshold = options.criticalThreshold || 0.9; // 90% critical level
        this.monitoringInterval = options.monitoringInterval || 2000; // 2 seconds
        this.gcInterval = options.gcInterval || 10000; // 10 seconds
        this.enableAutoCleanup = options.enableAutoCleanup !== false;
        this.enableProgressiveCleanup = options.enableProgressiveCleanup !== false;
        
        // Memory monitoring state
        this.isMonitoring = false;
        this.monitoringTimer = null;
        this.gcTimer = null;
        this.memoryHistory = [];
        this.maxHistorySize = 100;
        
        // Cleanup callbacks
        this.cleanupCallbacks = new Map();
        this.priorityLevels = ['critical', 'high', 'medium', 'low'];
        
        // Statistics
        this.stats = {
            monitoringStartTime: null,
            totalCleanups: 0,
            automaticCleanups: 0,
            manualCleanups: 0,
            memoryReclaimed: 0,
            peakMemoryUsage: 0,
            averageMemoryUsage: 0,
            gcTriggers: 0,
            thresholdBreaches: 0
        };
        
        // Memory leak detection
        this.leakDetection = {
            enabled: options.enableLeakDetection !== false,
            samples: [],
            maxSamples: 50,
            growthThreshold: 0.1, // 10% growth considered suspicious
            consecutiveGrowthLimit: 5
        };
        
        // Bind methods
        this.startMonitoring = this.startMonitoring.bind(this);
        this.stopMonitoring = this.stopMonitoring.bind(this);
        this.checkMemoryUsage = this.checkMemoryUsage.bind(this);
        this.triggerCleanup = this.triggerCleanup.bind(this);
        this.forceGarbageCollection = this.forceGarbageCollection.bind(this);
    }

    /**
     * Start memory monitoring
     */
    startMonitoring() {
        if (this.isMonitoring) return;
        
        this.isMonitoring = true;
        this.stats.monitoringStartTime = Date.now();
        
        // Start monitoring timer
        this.monitoringTimer = setInterval(() => {
            this.checkMemoryUsage();
        }, this.monitoringInterval);
        
        // Start periodic garbage collection
        if (this.enableAutoCleanup) {
            this.gcTimer = setInterval(() => {
                this.forceGarbageCollection();
            }, this.gcInterval);
        }
        
        console.log('Memory monitoring started');
        this.dispatchEvent('memoryManager:started', { timestamp: Date.now() });
    }

    /**
     * Stop memory monitoring
     */
    stopMonitoring() {
        if (!this.isMonitoring) return;
        
        this.isMonitoring = false;
        
        if (this.monitoringTimer) {
            clearInterval(this.monitoringTimer);
            this.monitoringTimer = null;
        }
        
        if (this.gcTimer) {
            clearInterval(this.gcTimer);
            this.gcTimer = null;
        }
        
        console.log('Memory monitoring stopped');
        this.dispatchEvent('memoryManager:stopped', { 
            timestamp: Date.now(),
            stats: this.getStats()
        });
    }

    /**
     * Check current memory usage and take action if needed
     */
    async checkMemoryUsage() {
        const memoryInfo = this.getMemoryInfo();
        
        // Add to history
        this.addToHistory(memoryInfo);
        
        // Update statistics
        this.updateStats(memoryInfo);
        
        // Check for memory leaks
        if (this.leakDetection.enabled) {
            this.detectMemoryLeaks(memoryInfo);
        }
        
        // Dispatch monitoring event
        this.dispatchEvent('memoryManager:check', { memoryInfo });
        
        // Take action based on memory usage
        if (memoryInfo.percentage >= this.criticalThreshold) {
            console.warn(`Critical memory usage: ${Math.round(memoryInfo.percentage * 100)}%`);
            await this.handleCriticalMemory(memoryInfo);
            
        } else if (memoryInfo.percentage >= this.memoryThreshold) {
            console.warn(`High memory usage: ${Math.round(memoryInfo.percentage * 100)}%`);
            await this.handleHighMemory(memoryInfo);
        }
        
        return memoryInfo;
    }

    /**
     * Get current memory information
     * @returns {Object} - Memory information
     */
    getMemoryInfo() {
        if (performance.memory) {
            const used = performance.memory.usedJSHeapSize;
            const total = performance.memory.totalJSHeapSize;
            const limit = performance.memory.jsHeapSizeLimit;
            
            return {
                used,
                total,
                limit,
                percentage: used / limit,
                available: limit - used,
                timestamp: Date.now(),
                usedMB: Math.round(used / 1024 / 1024),
                totalMB: Math.round(total / 1024 / 1024),
                limitMB: Math.round(limit / 1024 / 1024),
                availableMB: Math.round((limit - used) / 1024 / 1024)
            };
        }
        
        // Fallback for browsers without performance.memory
        return {
            used: 0,
            total: 0,
            limit: 0,
            percentage: 0,
            available: 0,
            timestamp: Date.now(),
            usedMB: 0,
            totalMB: 0,
            limitMB: 0,
            availableMB: 0,
            unavailable: true
        };
    }

    /**
     * Add memory info to history
     * @param {Object} memoryInfo - Memory information
     */
    addToHistory(memoryInfo) {
        this.memoryHistory.push(memoryInfo);
        
        // Limit history size
        if (this.memoryHistory.length > this.maxHistorySize) {
            this.memoryHistory.shift();
        }
    }

    /**
     * Update statistics
     * @param {Object} memoryInfo - Memory information
     */
    updateStats(memoryInfo) {
        // Update peak usage
        if (memoryInfo.percentage > this.stats.peakMemoryUsage) {
            this.stats.peakMemoryUsage = memoryInfo.percentage;
        }
        
        // Update average usage
        if (this.memoryHistory.length > 0) {
            const totalUsage = this.memoryHistory.reduce((sum, info) => sum + info.percentage, 0);
            this.stats.averageMemoryUsage = totalUsage / this.memoryHistory.length;
        }
        
        // Check for threshold breaches
        if (memoryInfo.percentage >= this.memoryThreshold) {
            this.stats.thresholdBreaches++;
        }
    }

    /**
     * Detect potential memory leaks
     * @param {Object} memoryInfo - Current memory information
     */
    detectMemoryLeaks(memoryInfo) {
        const samples = this.leakDetection.samples;
        samples.push({
            usage: memoryInfo.used,
            timestamp: memoryInfo.timestamp
        });
        
        // Limit samples
        if (samples.length > this.leakDetection.maxSamples) {
            samples.shift();
        }
        
        // Check for consistent growth
        if (samples.length >= this.leakDetection.consecutiveGrowthLimit) {
            const recentSamples = samples.slice(-this.leakDetection.consecutiveGrowthLimit);
            let consecutiveGrowth = 0;
            
            for (let i = 1; i < recentSamples.length; i++) {
                const growth = (recentSamples[i].usage - recentSamples[i-1].usage) / recentSamples[i-1].usage;
                
                if (growth > this.leakDetection.growthThreshold) {
                    consecutiveGrowth++;
                } else {
                    consecutiveGrowth = 0;
                    break;
                }
            }
            
            if (consecutiveGrowth >= this.leakDetection.consecutiveGrowthLimit - 1) {
                this.dispatchEvent('memoryManager:leakDetected', {
                    consecutiveGrowth,
                    samples: recentSamples,
                    timestamp: Date.now()
                });
                
                console.warn('Potential memory leak detected - consistent growth over time');
            }
        }
    }

    /**
     * Handle critical memory situation
     * @param {Object} memoryInfo - Memory information
     */
    async handleCriticalMemory(memoryInfo) {
        console.error('Critical memory situation - triggering emergency cleanup');
        
        // Trigger all cleanup levels
        await this.triggerCleanup('critical');
        await this.triggerCleanup('high');
        await this.triggerCleanup('medium');
        
        // Force garbage collection
        await this.forceGarbageCollection();
        
        // Wait and check again
        await this.delay(1000);
        const newMemoryInfo = this.getMemoryInfo();
        
        if (newMemoryInfo.percentage >= this.criticalThreshold) {
            // Still critical - trigger low priority cleanup
            await this.triggerCleanup('low');
            
            // Dispatch critical memory event
            this.dispatchEvent('memoryManager:critical', {
                before: memoryInfo,
                after: newMemoryInfo,
                timestamp: Date.now()
            });
        }
    }

    /**
     * Handle high memory situation
     * @param {Object} memoryInfo - Memory information
     */
    async handleHighMemory(memoryInfo) {
        if (this.enableProgressiveCleanup) {
            // Progressive cleanup - start with high priority
            await this.triggerCleanup('high');
            
            // Check if we need more cleanup
            const newMemoryInfo = this.getMemoryInfo();
            if (newMemoryInfo.percentage >= this.memoryThreshold) {
                await this.triggerCleanup('medium');
            }
        }
        
        // Trigger garbage collection
        await this.forceGarbageCollection();
    }

    /**
     * Register a cleanup callback
     * @param {string} name - Cleanup name
     * @param {Function} callback - Cleanup function
     * @param {string} priority - Priority level (critical, high, medium, low)
     */
    registerCleanup(name, callback, priority = 'medium') {
        if (!this.priorityLevels.includes(priority)) {
            throw new Error(`Invalid priority level: ${priority}`);
        }
        
        this.cleanupCallbacks.set(name, {
            callback,
            priority,
            registered: Date.now(),
            executions: 0,
            lastExecution: null,
            totalTime: 0
        });
        
        console.log(`Registered cleanup callback: ${name} (${priority} priority)`);
    }

    /**
     * Unregister a cleanup callback
     * @param {string} name - Cleanup name
     */
    unregisterCleanup(name) {
        if (this.cleanupCallbacks.delete(name)) {
            console.log(`Unregistered cleanup callback: ${name}`);
        }
    }

    /**
     * Trigger cleanup callbacks by priority
     * @param {string} priority - Priority level to trigger
     * @returns {Promise<Object>} - Cleanup results
     */
    async triggerCleanup(priority) {
        const startTime = Date.now();
        const results = {
            priority,
            executed: 0,
            successful: 0,
            failed: 0,
            totalTime: 0,
            errors: []
        };
        
        console.log(`Triggering ${priority} priority cleanup`);
        
        for (const [name, cleanup] of this.cleanupCallbacks) {
            if (cleanup.priority === priority) {
                results.executed++;
                
                try {
                    const cleanupStart = Date.now();
                    await cleanup.callback();
                    const cleanupTime = Date.now() - cleanupStart;
                    
                    cleanup.executions++;
                    cleanup.lastExecution = Date.now();
                    cleanup.totalTime += cleanupTime;
                    
                    results.successful++;
                    console.log(`Cleanup ${name} completed in ${cleanupTime}ms`);
                    
                } catch (error) {
                    results.failed++;
                    results.errors.push({
                        name,
                        error: error.message
                    });
                    console.error(`Cleanup ${name} failed:`, error);
                }
            }
        }
        
        results.totalTime = Date.now() - startTime;
        this.stats.totalCleanups++;
        this.stats.automaticCleanups++;
        
        this.dispatchEvent('memoryManager:cleanup', results);
        
        return results;
    }

    /**
     * Force garbage collection
     */
    async forceGarbageCollection() {
        const beforeMemory = this.getMemoryInfo();
        
        try {
            if (window.gc) {
                window.gc();
                this.stats.gcTriggers++;
                
                // Wait for GC to complete
                await this.delay(100);
                
                const afterMemory = this.getMemoryInfo();
                const reclaimed = beforeMemory.used - afterMemory.used;
                
                if (reclaimed > 0) {
                    this.stats.memoryReclaimed += reclaimed;
                    console.log(`Garbage collection reclaimed ${Math.round(reclaimed / 1024 / 1024)}MB`);
                }
                
                this.dispatchEvent('memoryManager:gc', {
                    before: beforeMemory,
                    after: afterMemory,
                    reclaimed,
                    timestamp: Date.now()
                });
                
            } else {
                console.warn('Manual garbage collection not available');
            }
        } catch (error) {
            console.error('Error during garbage collection:', error);
        }
    }

    /**
     * Manually trigger cleanup
     * @param {string} priority - Priority level (optional)
     */
    async manualCleanup(priority = null) {
        this.stats.manualCleanups++;
        
        if (priority) {
            return await this.triggerCleanup(priority);
        } else {
            // Trigger all priorities
            const results = [];
            for (const level of this.priorityLevels) {
                results.push(await this.triggerCleanup(level));
            }
            return results;
        }
    }

    /**
     * Get memory usage trend
     * @param {number} samples - Number of recent samples to analyze
     * @returns {Object} - Trend analysis
     */
    getMemoryTrend(samples = 10) {
        if (this.memoryHistory.length < 2) {
            return { trend: 'insufficient_data', samples: this.memoryHistory.length };
        }
        
        const recentSamples = this.memoryHistory.slice(-samples);
        const first = recentSamples[0];
        const last = recentSamples[recentSamples.length - 1];
        
        const change = last.percentage - first.percentage;
        const changePercent = (change / first.percentage) * 100;
        
        let trend = 'stable';
        if (changePercent > 5) {
            trend = 'increasing';
        } else if (changePercent < -5) {
            trend = 'decreasing';
        }
        
        return {
            trend,
            change,
            changePercent,
            samples: recentSamples.length,
            timespan: last.timestamp - first.timestamp,
            first: first.percentage,
            last: last.percentage
        };
    }

    /**
     * Get cleanup statistics
     * @returns {Object} - Cleanup statistics
     */
    getCleanupStats() {
        const cleanupStats = {};
        
        for (const [name, cleanup] of this.cleanupCallbacks) {
            cleanupStats[name] = {
                priority: cleanup.priority,
                executions: cleanup.executions,
                lastExecution: cleanup.lastExecution,
                averageTime: cleanup.executions > 0 ? cleanup.totalTime / cleanup.executions : 0,
                totalTime: cleanup.totalTime
            };
        }
        
        return cleanupStats;
    }

    /**
     * Get comprehensive statistics
     * @returns {Object} - Memory manager statistics
     */
    getStats() {
        const currentMemory = this.getMemoryInfo();
        const trend = this.getMemoryTrend();
        
        return {
            ...this.stats,
            currentMemory,
            trend,
            isMonitoring: this.isMonitoring,
            historySize: this.memoryHistory.length,
            cleanupCallbacks: this.cleanupCallbacks.size,
            cleanupStats: this.getCleanupStats(),
            leakDetection: {
                ...this.leakDetection,
                samples: this.leakDetection.samples.length
            }
        };
    }

    /**
     * Reset statistics
     */
    resetStats() {
        this.stats = {
            monitoringStartTime: this.isMonitoring ? Date.now() : null,
            totalCleanups: 0,
            automaticCleanups: 0,
            manualCleanups: 0,
            memoryReclaimed: 0,
            peakMemoryUsage: 0,
            averageMemoryUsage: 0,
            gcTriggers: 0,
            thresholdBreaches: 0
        };
        
        this.memoryHistory = [];
        this.leakDetection.samples = [];
    }

    /**
     * Dispatch custom event
     * @param {string} eventName - Event name
     * @param {Object} detail - Event detail
     */
    dispatchEvent(eventName, detail) {
        const event = new CustomEvent(eventName, { detail });
        document.dispatchEvent(event);
    }

    /**
     * Utility delay function
     * @param {number} ms - Milliseconds to delay
     */
    async delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    /**
     * Create a memory-aware wrapper for any function
     * @param {Function} fn - Function to wrap
     * @param {Object} options - Wrapper options
     * @returns {Function} - Wrapped function
     */
    createMemoryAwareWrapper(fn, options = {}) {
        const checkBefore = options.checkBefore !== false;
        const checkAfter = options.checkAfter !== false;
        const threshold = options.threshold || this.memoryThreshold;
        
        return async (...args) => {
            if (checkBefore) {
                const beforeMemory = this.getMemoryInfo();
                if (beforeMemory.percentage >= threshold) {
                    await this.triggerCleanup('high');
                }
            }
            
            const result = await fn(...args);
            
            if (checkAfter) {
                const afterMemory = this.getMemoryInfo();
                if (afterMemory.percentage >= threshold) {
                    await this.triggerCleanup('medium');
                }
            }
            
            return result;
        };
    }
}

export default MemoryManager;