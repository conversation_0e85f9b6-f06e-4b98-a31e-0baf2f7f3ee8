/**
 * Path Utility Functions
 * Helper functions for path manipulation and normalization
 */

const PathUtils = {
    /**
     * Normalize a path by converting backslashes to forward slashes
     * and resolving relative paths
     * @param {string} pathStr - Path to normalize
     * @param {string} rootStr - Optional root path for relative paths
     * @returns {string} - Normalized path
     */
    normalizePath(pathStr, rootStr = '') {
        if (typeof pathStr !== 'string') pathStr = '';
        if (typeof rootStr !== 'string') rootStr = '';
        
        let s = pathStr.replace(/\\/g, '/');
        let r = rootStr.replace(/\\/g, '/');
        
        if (r && r.length > 0 && !r.endsWith('/')) { 
            r += '/'; 
        }
        
        let finalPath = s;
        if (r && s.startsWith('.') && !s.startsWith(r)) { 
            finalPath = r + s; 
        }
        
        const parts = finalPath.split('/');
        const stack = [];
        
        for (const part of parts) {
            if (part === '.' || (part === '' && stack.length > 0 && stack[stack.length -1] !== '')) { 
                continue; 
            }
            
            if (part === '..') {
                if (stack.length > 0 && stack[stack.length - 1] !== '..') { 
                    stack.pop(); 
                }
                else if (!r || r.trim() === '') { 
                    stack.push('..'); 
                }
            } else { 
                stack.push(part); 
            }
        }
        
        return stack.join('/');
    },

    /**
     * Check if a path is a node module
     * @param {string} path - Path to check
     * @returns {boolean} - True if path is a node module
     */
    isNodeModule(path) {
        if (!path) return false;
        
        if (!path.startsWith('./') && !path.startsWith('../') && 
            !path.includes(':') && !path.startsWith('/')) {
            const lastSegment = path.split('/').pop();
            if (lastSegment && !lastSegment.includes('.')) {
                return true;
            }
        }
        
        const nodeModulePrefixes = ['@', 'node:', 'npm:'];
        for (const prefix of nodeModulePrefixes) {
            if (path.startsWith(prefix)) {
                return true;
            }
        }
        
        return false;
    },

    /**
     * Get the directory part of a path
     * @param {string} path - Full path
     * @returns {string} - Directory part of the path
     */
    getDirectory(path) {
        if (!path) return '';
        const normalizedPath = this.normalizePath(path);
        return normalizedPath.substring(0, normalizedPath.lastIndexOf('/') + 1);
    },

    /**
     * Get the filename part of a path
     * @param {string} path - Full path
     * @returns {string} - Filename part of the path
     */
    getFilename(path) {
        if (!path) return '';
        const normalizedPath = this.normalizePath(path);
        return normalizedPath.substring(normalizedPath.lastIndexOf('/') + 1);
    },

    /**
     * Get the extension of a file
     * @param {string} path - File path
     * @returns {string} - File extension (without the dot)
     */
    getExtension(path) {
        if (!path) return '';
        const filename = this.getFilename(path);
        const dotIndex = filename.lastIndexOf('.');
        return dotIndex === -1 ? '' : filename.substring(dotIndex + 1);
    }
};

// Export the utility object
export default PathUtils;
