/**
 * UI Controller Module
 * Handles UI interactions, event listeners, and DOM manipulations
 */

import DOMUtils from '../utils/dom-utils.js';

class UIController {
    constructor(options = {}) {
        // Configuration
        this.config = {
            ...options
        };
        
        // DOM Elements
        this.elements = {
            // Containers
            detailsPanel: DOMUtils.getById('details-panel-container'),
            fileDetailsPanel: DOMUtils.getById('file-details-panel'),
            visualization: DOMUtils.getById('mindmap-container'),
            progressBarContainer: DOMUtils.getById('progress-bar-container'),
            progressBar: DOMUtils.getById('progress-bar'),
            
            // Buttons
            closeDetailsPanelBtn: DOMUtils.getById('close-details-panel'),
            processFilesBtn: DOMUtils.getById('process-files-btn'),
            stopProcessBtn: DOMUtils.getById('stop-process-btn'),
            exportJsonBtn: DOMUtils.getById('export-json'),
            importJsonBtn: DOMUtils.getById('import-json'),
            socialMapBtn: DOMUtils.getById('social-map'),
            technicalMapBtn: DOMUtils.getById('technical-map'),
            logicChartBtn: DOMUtils.getById('logic-chart'),
            exportSvgBtn: DOMUtils.getById('export-svg'),
            exportPngBtn: DOMUtils.getById('export-png'),
            
            // Inputs
            projectFolderInput: DOMUtils.getById('project-folder-input'),
            jsonFileInput: DOMUtils.getById('json-file-input'),
            
            // Status and info
            status: DOMUtils.getById('status'),
            
            // Modals
            codePreviewModal: DOMUtils.getById('code-preview-modal'),
            codePreviewTitle: DOMUtils.getById('code-preview-title'),
            codePreviewContent: DOMUtils.getById('code-preview-content')
        };
        
        // Event handlers
        this.handlers = {};
        
        // Bind methods
        this.bindEvents = this.bindEvents.bind(this);
        this.updateStatus = this.updateStatus.bind(this);
        this.updateProgress = this.updateProgress.bind(this);
        this.showDetailsPanel = this.showDetailsPanel.bind(this);
        this.hideDetailsPanel = this.hideDetailsPanel.bind(this);
        this.showCodePreview = this.showCodePreview.bind(this);
        this.hideCodePreview = this.hideCodePreview.bind(this);
    }

    /**
     * Initialize the UI controller
     * @param {Object} eventHandlers - Event handlers for UI events
     * @returns {UIController} - This instance for chaining
     */
    initialize(eventHandlers = {}) {
        this.handlers = eventHandlers;
        this.bindEvents();
        return this;
    }

    /**
     * Bind event listeners to DOM elements
     */
    bindEvents() {
        // Details panel
        if (this.elements.closeDetailsPanelBtn) {
            this.elements.closeDetailsPanelBtn.addEventListener('click', this.hideDetailsPanel);
        }
        
        // File input
        if (this.elements.projectFolderInput) {
            this.elements.projectFolderInput.addEventListener('change', (event) => {
                if (this.handlers.onFileInputChange) {
                    this.handlers.onFileInputChange(event);
                }
            });
        }
        
        // Process buttons
        if (this.elements.processFilesBtn) {
            this.elements.processFilesBtn.addEventListener('click', () => {
                if (this.handlers.onProcessFiles) {
                    this.handlers.onProcessFiles();
                }
            });
        }
        
        if (this.elements.stopProcessBtn) {
            this.elements.stopProcessBtn.addEventListener('click', () => {
                if (this.handlers.onStopProcess) {
                    this.handlers.onStopProcess();
                }
            });
        }
        
        // Export/Import
        if (this.elements.exportJsonBtn) {
            this.elements.exportJsonBtn.addEventListener('click', () => {
                if (this.handlers.onExportJson) {
                    this.handlers.onExportJson();
                }
            });
        }
        
        if (this.elements.importJsonBtn) {
            this.elements.importJsonBtn.addEventListener('click', () => {
                this.elements.jsonFileInput.click();
            });
        }
        
        if (this.elements.jsonFileInput) {
            this.elements.jsonFileInput.addEventListener('change', (event) => {
                if (this.handlers.onImportJson) {
                    this.handlers.onImportJson(event);
                }
            });
        }
        
        // Visualization buttons
        if (this.elements.socialMapBtn) {
            this.elements.socialMapBtn.addEventListener('click', () => {
                if (this.handlers.onSwitchVisualizer) {
                    this.handlers.onSwitchVisualizer('social');
                }
            });
        }
        
        if (this.elements.technicalMapBtn) {
            this.elements.technicalMapBtn.addEventListener('click', () => {
                if (this.handlers.onSwitchVisualizer) {
                    this.handlers.onSwitchVisualizer('technical');
                }
            });
        }
        
        if (this.elements.logicChartBtn) {
            this.elements.logicChartBtn.addEventListener('click', () => {
                if (this.handlers.onSwitchVisualizer) {
                    this.handlers.onSwitchVisualizer('logic');
                }
            });
        }
        
        // Export buttons
        if (this.elements.exportSvgBtn) {
            this.elements.exportSvgBtn.addEventListener('click', () => {
                if (this.handlers.onExportSvg) {
                    this.handlers.onExportSvg();
                }
            });
        }
        
        if (this.elements.exportPngBtn) {
            this.elements.exportPngBtn.addEventListener('click', () => {
                if (this.handlers.onExportPng) {
                    this.handlers.onExportPng();
                }
            });
        }
        
        // Modal close buttons
        const closeButtons = document.querySelectorAll('.modal .close');
        closeButtons.forEach(button => {
            button.addEventListener('click', (event) => {
                const modal = event.target.closest('.modal');
                if (modal) {
                    modal.style.display = 'none';
                }
            });
        });
        
        // Close modals when clicking outside
        window.addEventListener('click', (event) => {
            if (event.target.classList.contains('modal')) {
                event.target.style.display = 'none';
            }
        });
    }

    /**
     * Update the status message
     * @param {string} message - Status message
     */
    updateStatus(message) {
        if (this.elements.status) {
            this.elements.status.textContent = message;
        }
    }

    /**
     * Update the progress bar
     * @param {number} percentage - Progress percentage (0-100)
     * @param {boolean} visible - Whether to show the progress bar
     */
    updateProgress(percentage, visible = true) {
        if (this.elements.progressBar) {
            this.elements.progressBar.style.width = `${percentage}%`;
            // Remove any text content from progress bar
            this.elements.progressBar.textContent = '';
        }

        if (this.elements.progressBarContainer) {
            this.elements.progressBarContainer.style.display = visible ? 'block' : 'none';
        }
    }

    /**
     * Show the details panel
     * @param {Object} data - Data to display in the panel
     */
    showDetailsPanel(data) {
        if (!this.elements.detailsPanel || !this.elements.fileDetailsPanel) return;
        
        // Update panel content
        if (data) {
            let html = '';
            
            // File info
            html += `
                <div class="details-section">
                    <h3 class="section-title">File Info</h3>
                    <div class="section-content">
                        <p><strong>Path:</strong> ${data.path || 'N/A'}</p>
                        <p><strong>Type:</strong> ${data.type || 'N/A'}</p>
                    </div>
                </div>
            `;
            
            // Imports
            if (data.imports && data.imports.length > 0) {
                html += `
                    <div class="details-section">
                        <h3 class="section-title imports-title">Imports (${data.imports.length})</h3>
                        <ul class="section-content">
                            ${data.imports.map(imp => `
                                <li data-path="${imp.path}">
                                    ${imp.path.split('/').pop()}
                                    ${imp.names && imp.names.length > 0 
                                        ? `<span class="import-names">${imp.names.join(', ')}</span>` 
                                        : ''}
                                </li>
                            `).join('')}
                        </ul>
                    </div>
                `;
            }
            
            // Exports
            if (data.exports && data.exports.length > 0) {
                html += `
                    <div class="details-section">
                        <h3 class="section-title exports-title">Exports (${data.exports.length})</h3>
                        <ul class="section-content">
                            ${data.exports.map(exp => `
                                <li>
                                    ${exp.name}
                                    ${exp.isDefault ? '<span class="export-default">(default)</span>' : ''}
                                </li>
                            `).join('')}
                        </ul>
                    </div>
                `;
            }
            
            this.elements.fileDetailsPanel.innerHTML = html;
            
            // Add click handlers for imports
            this.elements.fileDetailsPanel.querySelectorAll('li[data-path]').forEach(item => {
                item.addEventListener('click', () => {
                    const path = item.dataset.path;
                    if (this.handlers.onImportClick) {
                        this.handlers.onImportClick(path);
                    }
                });
            });
        }
        
        // Show panel
        DOMUtils.toggleClass(this.elements.detailsPanel, 'open', true);
        DOMUtils.toggleClass(document.querySelector('.content-scroll-area'), 'panel-open', true);
    }

    /**
     * Hide the details panel
     */
    hideDetailsPanel() {
        if (!this.elements.detailsPanel) return;
        
        DOMUtils.toggleClass(this.elements.detailsPanel, 'open', false);
        DOMUtils.toggleClass(document.querySelector('.content-scroll-area'), 'panel-open', false);
    }

    /**
     * Show code preview in a modal
     * @param {string} filePath - Path to the file
     * @param {number} lineNumber - Line number (1-based)
     * @param {string} itemName - Name of the item to highlight
     * @param {string} codeContent - Code content to display
     */
    showCodePreview(filePath, lineNumber, itemName, codeContent) {
        if (!this.elements.codePreviewModal || !this.elements.codePreviewTitle || !this.elements.codePreviewContent) return;
        
        // Set modal title
        this.elements.codePreviewTitle.textContent = `Preview: ${itemName} (line ${lineNumber} in ${filePath.split('/').pop()})`;
        
        // Format code with line numbers and syntax highlighting
        let formattedCode = '';
        
        if (codeContent) {
            const lines = codeContent.split('\n');
            const startLine = Math.max(1, lineNumber - 5);
            const endLine = Math.min(lines.length, lineNumber + 5);
            
            formattedCode += '<pre class="code-preview">';
            
            for (let i = startLine; i <= endLine; i++) {
                const isHighlighted = i === lineNumber;
                const lineClass = isHighlighted ? 'highlighted-line' : '';
                const lineContent = this.escapeHtml(lines[i - 1] || '');
                
                formattedCode += `<div class="code-line ${lineClass}">
                    <span class="line-number">${i}</span>
                    <span class="line-content">${lineContent}</span>
                </div>`;
            }
            
            formattedCode += '</pre>';
        } else {
            formattedCode = '<div class="error-message">Code content not available</div>';
        }
        
        // Set modal content
        this.elements.codePreviewContent.innerHTML = formattedCode;
        
        // Show modal
        this.elements.codePreviewModal.style.display = 'block';
    }

    /**
     * Hide code preview modal
     */
    hideCodePreview() {
        if (this.elements.codePreviewModal) {
            this.elements.codePreviewModal.style.display = 'none';
        }
    }

    /**
     * Escape HTML special characters
     * @param {string} text - Text to escape
     * @returns {string} - Escaped text
     */
    escapeHtml(text) {
        return String(text)
            .replace(/&/g, '&amp;')
            .replace(/</g, '&lt;')
            .replace(/>/g, '&gt;')
            .replace(/"/g, '&quot;')
            .replace(/'/g, '&#039;');
    }
}

// Export the UI controller class
export default UIController;
