/**
 * Data Model Core Module
 * Manages the dependency data model and provides methods for data manipulation
 */

class DependencyDataModel {
    constructor() {
        this.nodes = [];
        this.links = [];
        this.metadata = {};
        this.nodeMap = new Map(); // Map of node path to index
        this.selectedNode = null;
    }

    /**
     * Load data into the model
     * @param {Object} data - Data object with nodes, links, and metadata
     */
    loadData(data) {
        if (!data || !data.nodes || !data.links) {
            throw new Error('Invalid data format');
        }

        console.log('DataModel.loadData - Loading data:', data);
        console.log('DataModel.loadData - Nodes count:', data.nodes.length);
        console.log('DataModel.loadData - Links count:', data.links.length);

        this.nodes = data.nodes;
        this.links = data.links;
        this.metadata = data.metadata || {};
        
        // Build node map for quick lookups
        this.nodeMap.clear();
        this.nodes.forEach((node, index) => {
            if (node.path) {
                this.nodeMap.set(node.path, index);
            }
            // Also map by ID for quick lookups
            if (node.id) {
                this.nodeMap.set(node.id, index);
            }
        });
        
        console.log('DataModel.loadData - Node map size:', this.nodeMap.size);
        console.log('DataModel.loadData - Sample nodes:', this.nodes.slice(0, 3));
        
        // Reset selection
        this.selectedNode = null;
    }

    /**
     * Get all nodes in the model
     * @returns {Array} - Array of nodes
     */
    getNodes() {
        return this.nodes;
    }

    /**
     * Get all links in the model
     * @returns {Array} - Array of links
     */
    getLinks() {
        return this.links;
    }

    /**
     * Get metadata
     * @returns {Object} - Metadata object
     */
    getMetadata() {
        return this.metadata;
    }

    /**
     * Get a node by its index
     * @param {number} index - Node index
     * @returns {Object|null} - Node object or null if not found
     */
    getNodeByIndex(index) {
        return (index >= 0 && index < this.nodes.length) ? this.nodes[index] : null;
    }

    /**
     * Get a node by its path
     * @param {string} path - Node path
     * @returns {Object|null} - Node object or null if not found
     */
    getNodeByPath(path) {
        const index = this.nodeMap.get(path);
        return (index !== undefined) ? this.nodes[index] : null;
    }

    /**
     * Get a node by its ID
     * @param {string} id - Node ID
     * @returns {Object|null} - Node object or null if not found
     */
    getNodeById(id) {
        return this.nodes.find(node => node.id === id) || null;
    }

    /**
     * Get all incoming links for a node
     * @param {number} nodeIndex - Node index
     * @returns {Array} - Array of incoming links
     */
    getIncomingLinks(nodeIndex) {
        return this.links.filter(link => link.target === nodeIndex);
    }

    /**
     * Get all outgoing links for a node
     * @param {number} nodeIndex - Node index
     * @returns {Array} - Array of outgoing links
     */
    getOutgoingLinks(nodeIndex) {
        return this.links.filter(link => link.source === nodeIndex);
    }

    /**
     * Get all dependencies for a node
     * @param {number} nodeIndex - Node index
     * @returns {Array} - Array of dependency nodes
     */
    getDependencies(nodeIndex) {
        const outgoingLinks = this.getOutgoingLinks(nodeIndex);
        return outgoingLinks.map(link => this.getNodeByIndex(link.target));
    }

    /**
     * Get all dependents for a node
     * @param {number} nodeIndex - Node index
     * @returns {Array} - Array of dependent nodes
     */
    getDependents(nodeIndex) {
        const incomingLinks = this.getIncomingLinks(nodeIndex);
        return incomingLinks.map(link => this.getNodeByIndex(link.source));
    }

    /**
     * Set the selected node
     * @param {number|string|null} nodeIdentifier - Node index, node ID, or null to clear selection
     */
    setSelectedNode(nodeIdentifier) {
        if (nodeIdentifier === null || nodeIdentifier === undefined) {
            this.selectedNode = null;
            return;
        }
        
        // If it's a number, treat it as an index
        if (typeof nodeIdentifier === 'number') {
            this.selectedNode = nodeIdentifier;
            return;
        }
        
        // If it's a string, treat it as a node ID and find the corresponding index
        if (typeof nodeIdentifier === 'string') {
            const nodeIndex = this.nodes.findIndex(node => node.id === nodeIdentifier);
            this.selectedNode = nodeIndex >= 0 ? nodeIndex : null;
            return;
        }
        
        // Invalid type
        this.selectedNode = null;
    }

    /**
     * Get the selected node
     * @returns {Object|null} - Selected node or null if none selected
     */
    getSelectedNode() {
        return (this.selectedNode !== null && this.selectedNode >= 0) ? this.getNodeByIndex(this.selectedNode) : null;
    }

    /**
     * Filter nodes by type
     * @param {string|Array} types - Node type(s) to include
     * @returns {Array} - Filtered nodes
     */
    filterNodesByType(types) {
        const typeArray = Array.isArray(types) ? types : [types];
        return this.nodes.filter(node => typeArray.includes(node.type));
    }

    /**
     * Filter links by type
     * @param {string|Array} types - Link type(s) to include
     * @returns {Array} - Filtered links
     */
    filterLinksByType(types) {
        const typeArray = Array.isArray(types) ? types : [types];
        return this.links.filter(link => typeArray.includes(link.type));
    }

    /**
     * Search nodes by name or path
     * @param {string} query - Search query
     * @returns {Array} - Matching nodes
     */
    searchNodes(query) {
        if (!query) return [];
        
        const lowerQuery = query.toLowerCase();
        return this.nodes.filter(node => {
            return node.name.toLowerCase().includes(lowerQuery) || 
                   node.path.toLowerCase().includes(lowerQuery);
        });
    }

    /**
     * Get a subgraph centered on a specific node
     * @param {number} nodeIndex - Center node index
     * @param {number} depth - Maximum depth of the subgraph
     * @returns {Object} - Subgraph with nodes and links
     */
    getSubgraph(nodeIndex, depth = 1) {
        if (nodeIndex === null || nodeIndex < 0 || nodeIndex >= this.nodes.length) {
            return { nodes: [], links: [] };
        }
        
        const visitedNodes = new Set([nodeIndex]);
        const nodesToProcess = [{ index: nodeIndex, depth: 0 }];
        const subgraphNodes = [];
        const subgraphLinks = [];
        const nodeIndexMap = new Map();
        
        while (nodesToProcess.length > 0) {
            const { index, depth: currentDepth } = nodesToProcess.shift();
            
            if (currentDepth > depth) continue;
            
            // Add node to subgraph
            const newIndex = subgraphNodes.length;
            subgraphNodes.push(this.nodes[index]);
            nodeIndexMap.set(index, newIndex);
            
            if (currentDepth < depth) {
                // Process dependencies
                const outgoingLinks = this.getOutgoingLinks(index);
                outgoingLinks.forEach(link => {
                    if (!visitedNodes.has(link.target)) {
                        visitedNodes.add(link.target);
                        nodesToProcess.push({ index: link.target, depth: currentDepth + 1 });
                    }
                });
                
                // Process dependents
                const incomingLinks = this.getIncomingLinks(index);
                incomingLinks.forEach(link => {
                    if (!visitedNodes.has(link.source)) {
                        visitedNodes.add(link.source);
                        nodesToProcess.push({ index: link.source, depth: currentDepth + 1 });
                    }
                });
            }
        }
        
        // Add links between nodes in the subgraph
        this.links.forEach(link => {
            if (visitedNodes.has(link.source) && visitedNodes.has(link.target)) {
                subgraphLinks.push({
                    source: nodeIndexMap.get(link.source),
                    target: nodeIndexMap.get(link.target),
                    type: link.type,
                    names: link.names
                });
            }
        });
        
        return { nodes: subgraphNodes, links: subgraphLinks };
    }
}

// Export the data model class
export default DependencyDataModel;
